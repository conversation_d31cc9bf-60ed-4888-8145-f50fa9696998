# ================================
# Python 相关
# ================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.development
.env.test
.env.production
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ================================
# IDE 和编辑器
# ================================

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ================================
# 操作系统
# ================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ================================
# 日志和临时文件
# ================================

# 日志文件
*.log
logs/
log/
*.log.*
*.out

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej
*.swp
*.swo

# ================================
# 数据库
# ================================

# SQLite
*.sqlite
*.sqlite3
*.db
*.db-journal
*.db-wal
*.db-shm

# Redis
dump.rdb
*.rdb

# MySQL
*.sql
*.dump

# ================================
# 缓存和编译文件
# ================================

# 各种缓存目录
.cache/
*.cache
.sass-cache/
.npm/
.yarn/
node_modules/

# 编译文件
*.com
*.class
*.dll
*.exe
*.o
*.so
*.dylib

# ================================
# 项目特定文件
# ================================

# 上传文件目录
uploads/
temp_*/
**/temp_*/

# 模型文件
model/
models/weights/
*.pkl
*.joblib
*.h5
*.pb
*.onnx
*.pt
*.pth

# 数据文件
data/temp/
data/cache/
*.csv
*.xlsx
*.xls
*.json
*.jsonl
!requirements*.txt
!pyproject.toml
!*.json  # 保留配置文件

# Nacos 配置
nacos-data/
**/nacos-data/

# 文档处理临时文件
rtf/
*.docx
*.doc
*.pdf
!template/**/*.docx
!template/**/*.pdf

# 压缩文件
*.tar.gz
*.tgz
*.zip
*.rar
*.7z

# 网络日志
network_logs/

# 测试文件
test/
**/test_*.py
!tests/

# Excel 导入文件
excel_import_files/
api/excel_import_files/

# 协议和报告临时文件
temp_protocols/
temp_medical_reports/
api/temp_protocols/
api/temp_medical_reports/

# ================================
# 容器和部署
# ================================

# Docker
.dockerignore
Dockerfile.local
docker-compose.override.yml
.docker/

# Kubernetes
*.kubeconfig

# ================================
# 安全和密钥
# ================================

# 密钥文件
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*

# 配置文件中的敏感信息
.env.local
.env.*.local
config/local_*
**/secrets/
**/credentials/

# ================================
# 性能和监控
# ================================

# 性能分析文件
*.prof
*.pstats
*.cprof

# 监控数据
*.pid
*.sock

# ================================
# 其他工具
# ================================

# Ruff
.ruff_cache/

# Coverage
.coverage
.coverage.*
htmlcov/

# Pytest
.pytest_cache/

# Tox
.tox/

# UV (Python 包管理器)
.uv/

# 备份文件
*.bak
*.backup
*~