# From ubuntu:22.04
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/open/python:3.11.0_202501072003

# 设置环境变量
ENV APP_NAME=yiya-ai-bot \
    APP_HOME=/root \
    LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh

COPY deb-sources.list /etc/apt/sources.list

# 创建必要目录并安装系统依赖
RUN mkdir -p $APP_HOME/$APP_NAME/bin \
             $APP_HOME/$APP_NAME/conf \
             $APP_HOME/$APP_NAME/target \
             $APP_HOME/logs/app \
             $APP_HOME/logs/supervisord && \
    apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
        unar \
        libgl1-mesa-glx \
        libglib2.0-0 && \
    rm -rf /var/lib/apt/lists/*

# 将应用启动脚本、健康检查脚本、nginx配置文件复制到镜像中
COPY environment/common/app/conf/requirements.txt /home/<USER>/$APP_NAME/requirements.txt

# 安装Python依赖 - 合并所有pip install命令以减少镜像层
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && \
    pip install -r /home/<USER>/$APP_NAME/requirements.txt && \
    pip install \
        circus \
        opencv-python \
        markdown-it-py==3.0.0 \
        markdownify==0.14.1 \
        markitdown==0.0.1a3 \
        python-docx \
        PyMuPDF \
        patool \
        pdf2image==1.17.0 \
        orjson==3.10.18 \
        pymysql==1.1.1 \
        celery==5.5.3 \
        redis==6.2.0 \
        pydantic-settings==2.10.1
#RUN pip install --pre torch --index-url https://download.pytorch.org/whl/nightly/cu124


ENV PATH="$PATH:$APP_HOME/.local/bin:/usr/local/python${PYTHON_MAIN_VER}/bin"

# 复制应用文件并设置权限
COPY environment/common/app/ $APP_HOME/$APP_NAME/
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini
COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 创建启动脚本并设置权限 - 合并RUN命令
RUN echo "$APP_HOME/$APP_NAME/bin/appctl.sh start" >> $APP_HOME/start.sh && \
    echo "$APP_HOME/$APP_NAME/bin/preload.sh" >> $APP_HOME/health.sh && \
    chmod -R a+x ${APP_HOME}/$APP_NAME/bin/ && \
    chmod +x ${APP_HOME}/*.sh

WORKDIR $APP_HOME/$APP_NAME

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]

