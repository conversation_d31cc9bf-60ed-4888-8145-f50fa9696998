import logging
import os
import sys
import time

import nest_asyncio
from fastapi import APIRouter

from models.file_diff_request import FileDiffRequest
from models.file_diff_request import SingleFileDiffRequest
from models.file_diff_request import DocDiffConvertRequest
from models.result import make_success, make_fail
from utils.docx_utils import compare_docx_files
from utils.oss_utils import download_file
from utils.docx_utils import get_doc_diff_data_v3
from utils.docx_utils import parse_icf_first_table
from utils.docx_utils import get_doc_diff_data_v2
from utils.docx_utils import generate_change_report

logger = logging.getLogger(name=__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

nest_asyncio.apply()  # 允许嵌套的事件循环

router = APIRouter(include_in_schema=False)

# 创建存储文件的文件夹
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)


@router.post('/doc/diff')
async def compair_doc_diff(request: FileDiffRequest):
    start = time.time()

    try:
        # 参数检查
        if not request.original_file or not request.revised_file:
            return make_fail(400, "original_file and revised_file are required.")

        # OSS 文件下载
        original_file_path = os.path.join(UPLOAD_FOLDER, request.original_file.file_name)
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.revised_file.file_name)
        download_file(request.original_file.file_key, original_file_path)
        download_file(request.revised_file.file_key, revised_file_path)

        # 比较文件差异
        changes = compare_docx_files(original_file_path, revised_file_path)


        # 返回
        # os.remove(original_file_path)
        # os.remove(revised_file_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(changes, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))
@router.post('/doc/single_diff/v3')
async def compair_doc_diff(request: SingleFileDiffRequest):
    start = time.time()

    try:
        # 参数检查
        if not request.file:
            return make_fail(400, "file are required.")

        # OSS 文件下载
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.file.file_name)
        download_file(request.file.file_key, revised_file_path)

        # 比较文件差异
        changes = get_doc_diff_data_v3(revised_file_path)

        # 提取方案号，修订前后版本号
        icf_first_table_info=parse_icf_first_table(revised_file_path)

        # 组合返回数据
        response_data = {
            "changes": changes,
            "icf_first_table_info": icf_first_table_info
        }
        # 返回
        # os.remove(original_file_path)
        # os.remove(revised_file_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(response_data, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))

@router.post('/doc/single_diff/v2')
async def compair_doc_diff2(request: SingleFileDiffRequest):
    start = time.time()

    try:
        # 参数检查
        if not request.file:
            return make_fail(400, "file are required.")

        # OSS 文件下载
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.file.file_name)
        download_file(request.file.file_key, revised_file_path)

        # 比较文件差异
        changes = get_doc_diff_data_v2(revised_file_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(changes, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))

@router.post('/doc/diff/report')
async def doc_report(request: DocDiffConvertRequest):
    start = time.time()
    try:
        # 参数检查
        if not request.revised_json or not request.summary_result or not request.icf_first_table_info:
            return make_fail(400, "revised_json or summary_result are required.")
        # 组装修订内容
        current_file_path = os.path.dirname(__file__)
        template_path = os.path.join(current_file_path, '..', 'template/diff/revised_diff_template.docx')
        file = generate_change_report(request.revised_json, request.summary_result,request.icf_first_table_info, template_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(file, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))
