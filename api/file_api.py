import asyncio
import logging
import os
import time
import uuid
from datetime import datetime

from fastapi import APIRouter, Request

from config.env_config import UPLOAD_FOLDER
from models.file_info import FileInfo
from models.result import make_success, make_fail
from servers.file_server import build_oss_key
from utils.file_utils import detect_file_type
from utils.oss_utils import write_file, get_file_url
from utils.request_utils import get_filename_from_request
from servers.file_server import upload_and_return_file_info

router = APIRouter(include_in_schema=False)
logger = logging.getLogger(name=__name__)


@router.post('/file/upload')
async def upload(request: Request):
    start = time.time()
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    filename = f"{formatted_time}.txt"

    try:
        filename2 = get_filename_from_request(request)
        if filename2:
            filename = filename2

        current_file_path = os.path.dirname(__file__)
        file_path = os.path.join(current_file_path, '..', UPLOAD_FOLDER, str(uuid.uuid1()) + "-" + filename)

        content = await request.body()

        # 保存上传的文件
        with open(file_path, "wb") as f:
            f.write(content)

        # 上传文件到OSS
        key = build_oss_key(filename)
        write_file(file_path, key)

        # ASR
        file_url = get_file_url(key)
        res = FileInfo(file_key=key, file_name=filename, file_url=file_url)

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回结果
        end = time.time()
        return make_success(res, int(end - start))
    except Exception as e:
        logging.error("File Upload Failed: ", e)
        return make_fail(500, str(e))


@router.post('/file/pdf_to_images')
async def convert_pdf_to_images(request: Request):
    # 1. 下载文件或直接读取
    from utils.request_utils import upload

    start = time.time()

    try:
        file_info = asyncio.run(upload(request))

        if not file_info:
            return make_fail(400, "file upload failed.")

        # 2. 判断文件类型
        file_type = detect_file_type(file_info.file_path)

        file_info_list = []

        # 3. 根据类型处理
        if file_type == 'image':
            file_info_list.append(file_info)
        elif file_type == 'pdf':
            from utils.pdf_util import pdf_to_images
            now = datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            output_folder = f"{UPLOAD_FOLDER}/{formatted_time}/{file_info.file_name}"

            if not os.path.exists(output_folder):
                os.makedirs(output_folder)

            images = pdf_to_images(file_info.file_path, output_folder, dpi=200)

            for image in images:
                file_info = upload_and_return_file_info(image.file_path, image.file_name)
                file_info.file_path = None
                file_info_list.append(file_info)
        else:
            return make_fail(400, "Unsupported file type!")

        # 4. 返回
        return make_success(file_info_list, time_cost=int(time.time() - start) * 1000)
    except Exception as e:
        logger.error(f"Error processing file: ", e)
        return make_fail(500, "Internal Server Error")
