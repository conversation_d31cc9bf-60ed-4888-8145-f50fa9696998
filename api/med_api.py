import asyncio
import os
import threading
import gc
import sys
import tempfile
import multiprocessing
from pathlib import Path
from urllib.parse import urlparse
from fastapi import APIRouter, Request, Form, HTTPException
from fastapi.responses import JSONResponse
import httpx
from concurrent.futures import ProcessPoolExecutor
from med_write import MAX_FILE_SIZE
import psutil
import logging
import requests
logger = logging.getLogger(__name__)


# —— 1. 修复后的可回收 Executor 管理器 —— #
class RecyclableExecutor:
    def __init__(self, max_workers: int, recycle_after_tasks: int):
        self.max_workers = max_workers
        self.recycle_after = recycle_after_tasks
        self._lock = threading.Lock()
        self._executor = None
        self._task_count = 0
        self._start_new_pool()

    def _start_new_pool(self):
        with self._lock:
            # 关闭旧池
            if self._executor:
                try:
                    self._executor.shutdown(wait=True, cancel_futures=True)
                    logger.info("🔄 旧进程池已关闭")
                except Exception as e:
                    logger.warning(f"关闭旧进程池时出错: {e}")
                finally:
                    del self._executor

            # 根据环境选择不同的进程创建方式
            try:
                # 在 macOS 上强制使用 spawn，其他 Unix 尝试 fork，Windows 默认 spawn
                if sys.platform == 'darwin':  # 'darwin' 是 macOS
                    ctx = multiprocessing.get_context('spawn')
                    logger.info("🍎 macOS 环境，强制使用 spawn 模式创建进程池")
                elif sys.platform != 'win32':
                    try:
                        ctx = multiprocessing.get_context('fork')
                        logger.info("使用 fork 模式创建进程池")
                    except RuntimeError:
                        ctx = multiprocessing.get_context('spawn')
                        logger.info("Fork 不支持，使用 spawn 模式创建进程池")
                else:
                    ctx = multiprocessing.get_context('spawn')
                    logger.info("Windows 环境，使用 spawn 模式创建进程池")

                self._executor = ProcessPoolExecutor(
                    max_workers=self.max_workers,
                    mp_context=ctx
                )
            except Exception as e:
                logger.warning(f"创建进程池失败，使用默认方式: {e}")
                self._executor = ProcessPoolExecutor(max_workers=self.max_workers)

            self._task_count = 0
            logger.info(f"✅ 新进程池已创建，最大工作进程数: {self.max_workers}")
            gc.collect()

    @property
    def executor(self):
        if self._executor is None:
            self._start_new_pool()
        return self._executor

    def submit(self, loop, fn, *args):
        """提交任务到进程池"""
        fut = loop.run_in_executor(self.executor, fn, *args)

        def cleanup_callback(future):
            with self._lock:
                self._task_count += 1
                logger.info(f"任务完成，当前任务计数: {self._task_count}/{self.recycle_after}")
                if self._task_count >= self.recycle_after:
                    # 延迟重建，避免正在执行的任务被中断
                    logger.info("达到回收阈值，将重建进程池")
                    threading.Timer(1.0, self._start_new_pool).start()

        fut.add_done_callback(cleanup_callback)
        return fut

    def manual_recycle(self):
        """手动回收进程池"""
        logger.info("手动触发进程池回收")
        self._start_new_pool()


# —— 内存监控函数 —— #
def log_memory_usage(stage: str):
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        logger.info(f"[{stage}] 内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
    except Exception as e:
        logger.warning(f"获取内存信息失败: {e}")


# —— 在子进程中运行的函数 —— #
def _run_extract(protocol_file, template_file, tfl_file, run_id, corp):
    """在子进程中运行，确保内存完全隔离"""
    import gc
    import os
    import sys
    import logging
    import traceback  # 添加这个导入

    # 设置子进程日志
    logger = logging.getLogger(__name__)

    try:
        # 在子进程中重新导入所有必要模块
        sys.path.insert(0, os.getcwd())
        from med_write.utils import ExtractionResult

        logger.info(f"子进程开始处理任务: {run_id}, PID: {os.getpid()}")

        extractor = ExtractionResult()
        if corp == "fosun":
            resp = extractor.extract_write_upload_fosun(
                protocol_file,
                template_file,
                tfl_file,
                run_id
            )
        else:
            resp = extractor.extract_write_upload(
                protocol_file,
                template_file,
                tfl_file,
                run_id
            )

        result = resp
        logger.info(f"子进程任务处理完成: {run_id}")
        return result

    except Exception as e:
        # 获取完整的错误堆栈信息
        error_traceback = traceback.format_exc()
        logger.error(f"子进程任务处理失败: {run_id}")
        logger.error(f"错误类型: {type(e).__name__}")
        logger.error(f"错误信息: {str(e)}")
        logger.error(f"完整堆栈信息:\n{error_traceback}")

        # 重新抛出异常，但包含更多信息
        raise Exception(f"子进程处理失败 [{type(e).__name__}]: {str(e)}\n堆栈信息:\n{error_traceback}")
    finally:
        # 子进程结束时强制清理
        gc.collect()
        logger.info(f"子进程资源清理完成: {run_id}")


# —— 创建可回收池 —— #
executor_mgr = RecyclableExecutor(
    max_workers=1,
    recycle_after_tasks=1
)

# 临时下载目录
DOWNLOAD_DIR = Path("../data/temp/download").absolute()
DOWNLOAD_DIR.mkdir(parents=True, exist_ok=True)


def schedule_delete(path: str, delay: int = 300):
    async def _del():
        await asyncio.sleep(delay)
        try:
            if os.path.exists(path):
                os.remove(path)
                logger.info(f"定时删除文件成功: {path}")
        except OSError as e:
            logger.warning(f"定时删除文件失败: {path}, 错误: {e}")

    asyncio.create_task(_del())


async def download_to_tmp(
    url: str,
    request: Request,
    auto_delete: bool = True,
    delay: int = 300
) -> str:
    """
    流式下载文件到本地临时目录，控制内存占用并在发生非 2xx 或其他异常时正确关闭底层流。
    """
    parsed = urlparse(url)
    suffix = Path(parsed.path).suffix or ".docx"
    tmp_path = DOWNLOAD_DIR / (next(tempfile._get_candidate_names()) + suffix)

    client: httpx.AsyncClient = request.app.state.async_client
    try:
        async with client.stream("GET", url) as resp:
            # 先检查 HTTP 状态码，非 2xx 则先 drain 再抛出
            try:
                resp.raise_for_status()
            except httpx.HTTPStatusError as e:
                # drain 底层 MemoryObjectReceiveStream，确保 close
                await resp.aread()
                raise HTTPException(
                    status_code=e.response.status_code,
                    detail=f"下载失败，HTTP {e.response.status_code}"
                )

            # 状态OK，开始流式写文件
            with open(tmp_path, "wb") as f:
                async for chunk in resp.aiter_bytes(1024 * 1024):
                    f.write(chunk)

        logger.info(f"文件下载成功: {tmp_path}")
        if auto_delete:
            schedule_delete(str(tmp_path), delay)
        return str(tmp_path)

    except HTTPException:
        # 已经是明确的 HTTPException，直接向上抛
        raise
    except Exception as e:
        # 其他异常路径，也清理临时文件
        if tmp_path.exists():
            tmp_path.unlink()
        raise HTTPException(status_code=400, detail=f"下载失败: {e}")


router = APIRouter()


@router.post("/extract_and_upload")
async def extract(request: Request,
                  protocol_url: str = Form(...),
                  tfl_url: str = Form(...),
                  workflow_run_id: str = Form(...),
                  corp: str = Form("jietong")):
    # 检查当前内存使用
    try:
        process = psutil.Process(os.getpid())
        current_memory = process.memory_info().rss / 1024 / 1024

        # 如果内存使用超过阈值，拒绝处理
        if current_memory > 2000:  # 2GB 阈值
            logger.warning(f"内存使用过高: {current_memory:.2f}MB，拒绝新请求")
            raise HTTPException(status_code=503, detail="服务器内存使用过高，请稍后重试")
    except Exception as e:
        logger.warning(f"内存检查失败: {e}")

    log_memory_usage("开始处理")

    protocol_file = None
    tfl_file = None

    try:
        # 检查 protocol 文件大小
        try:
            protocol_response = await asyncio.get_event_loop().run_in_executor(
                None, lambda: requests.head(protocol_url, timeout=30)
            )
            protocol_size = int(protocol_response.headers.get('content-length', 0))
            if protocol_size > MAX_FILE_SIZE:
                logger.warning(f"Protocol文件过大: {protocol_size / 1024 / 1024:.2f}MB，超过20MB限制")
                raise HTTPException(
                    status_code=413,
                    detail=f"Protocol文件过大({protocol_size / 1024 / 1024:.2f}MB)，不能超过20MB"
                )
        except requests.RequestException as e:
            logger.warning(f"无法获取protocol文件大小: {e}")

        # 检查 tfl 文件大小
        try:
            tfl_response = await asyncio.get_event_loop().run_in_executor(
                None, lambda: requests.head(tfl_url, timeout=30)
            )
            tfl_size = int(tfl_response.headers.get('content-length', 0))
            if tfl_size > MAX_FILE_SIZE:
                logger.warning(f"TFL文件过大: {tfl_size / 1024 / 1024:.2f}MB，超过20MB限制")
                raise HTTPException(
                    status_code=413,
                    detail=f"TFL文件过大({tfl_size / 1024 / 1024:.2f}MB)，不能超过20MB"
                )
        except requests.RequestException as e:
            logger.warning(f"无法获取tfl文件大小: {e}")

        if corp == "fosun":
            template_file = "./med_write/data/template_fosun.docx"
        else:
            template_file = "./med_write/data/template.docx"

        # 下载文件
        protocol_file, tfl_file = await asyncio.gather(
            download_to_tmp(protocol_url, request, auto_delete=False),
            download_to_tmp(tfl_url, request, auto_delete=False),
        )

        # 下载后再次检查实际文件大小（双重保险）
        if os.path.exists(protocol_file):
            actual_protocol_size = os.path.getsize(protocol_file)
            if actual_protocol_size > MAX_FILE_SIZE:
                logger.warning(f"Protocol文件实际大小过大: {actual_protocol_size / 1024 / 1024:.2f}MB")
                raise HTTPException(
                    status_code=413,
                    detail=f"Protocol文件过大({actual_protocol_size / 1024 / 1024:.2f}MB)，不能超过20MB"
                )

        if os.path.exists(tfl_file):
            actual_tfl_size = os.path.getsize(tfl_file)
            if actual_tfl_size > MAX_FILE_SIZE:
                logger.warning(f"TFL文件实际大小过大: {actual_tfl_size / 1024 / 1024:.2f}MB")
                raise HTTPException(
                    status_code=413,
                    detail=f"TFL文件过大({actual_tfl_size / 1024 / 1024:.2f}MB)，不能超过20MB"
                )

        log_memory_usage("文件下载完成")

        # 使用进程池处理
        loop = asyncio.get_running_loop()
        fut = executor_mgr.submit(
            loop, _run_extract,
            protocol_file, template_file, tfl_file, workflow_run_id, corp
        )

        # 设置超时
        try:
            data = await asyncio.wait_for(fut, timeout=300)  # 5分钟超时
        except asyncio.TimeoutError:
            logger.error("处理超时")
            raise HTTPException(status_code=408, detail="处理超时")

        log_memory_usage("处理完成")

        return JSONResponse({
            "success": True,
            "code": 0,
            "message": "文件上传成功",
            "result": data.get("result") if isinstance(data, dict) else data
        })

    except HTTPException:
        raise
    except Exception as e:
        # 记录完整的错误信息
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"主进程处理失败：{type(e).__name__}: {str(e)}")
        logger.error(f"完整堆栈信息:\n{error_traceback}")
        raise HTTPException(status_code=500, detail=f"处理失败：{str(e)}")

    finally:
        # 清理临时文件
        for temp_file in [protocol_file, tfl_file]:
            if temp_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logger.info(f"清理临时文件: {temp_file}")
                except OSError as e:
                    logger.warning(f"清理临时文件失败: {temp_file}, 错误: {e}")

        # 强制回收
        # executor_mgr.manual_recycle()

        # 多次垃圾回收
        for _ in range(3):
            gc.collect()

        log_memory_usage("清理后")


@router.post("/executor/recycle")
async def manual_recycle():
    """手动回收进程池的端点"""
    executor_mgr.manual_recycle()
    gc.collect()
    log_memory_usage("手动回收后")
    return {"recycled": True, "message": "进程池已手动回收"}


@router.get("/memory/status")
async def memory_status():
    """内存状态查询端点"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            "memory_mb": round(memory_info.rss / 1024 / 1024, 2),
            "memory_percent": process.memory_percent(),
            "pid": os.getpid(),
            "executor_task_count": executor_mgr._task_count
        }
    except Exception as e:
        return {"error": str(e)}
