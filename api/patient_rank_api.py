#!/usr/bin/env python
# -*- coding: utf-8 -*-

from logger.logger import app_logger
import time
from fastapi import APIRouter, HTTPException
from models.result import make_success, make_fail
from models.patient_rank_vo import PatientRankingRequest
from utils.patient_rank_utils import rank_and_export

# 按照公司风格，创建 APIRouter 实例
router = APIRouter(
    prefix="/patients",  # 为此路由下的所有路径添加前缀
    tags=["Patient Ranking"]  # 在OpenAPI文档中进行分组
)


@router.post('/rank-and-export', summary="对患者进行排名并导出Excel报告")
async def generate_patient_ranking_report(request: PatientRankingRequest):
    """
    接收多位患者的计分结果，进行排名，生成详细的Excel报告，
    并将其上传至OSS，最终返回可供下载的URL。

    - **patient_json_strings**: 包含每个患者详细得分的JSON字符串列表。
    - **business_id**: 用于生成报告文件名的业务标识符。
    """
    start_time = time.time()  # 使用 start_time 保持风格一致
    app_logger.info(f"Received request to rank and export for business_id: {request.business_id}")

    try:
        # 1. 从请求体中获取参数
        patient_jsons = request.patient_json_strings
        biz_id = request.business_id

        # 输入验证
        if not patient_jsons:
            app_logger.warning(f"Request for business_id: {biz_id} received with empty patient_json_strings.")
            raise HTTPException(status_code=400, detail="patient_json_strings 列表不能为空")

        # 2. 调用核心业务逻辑
        #    该函数负责处理排序、生成Excel、上传OSS并返回URL的全部流程
        report_url = rank_and_export(
            patient_json_strings=patient_jsons,
            business_id=biz_id
        )

        # 3. 构造并返回成功响应
        response_data = {"report_url": report_url}

        # --- 主要修改点 ---
        # 计算以毫秒为单位的耗时
        duration = time.time() - start_time
        time_cost_ms = int(duration * 1000)
        # --- 修改结束 ---

        app_logger.info(
            f"Successfully generated report for business_id: {biz_id}. URL: {report_url}. Cost: {time_cost_ms}ms")

        return make_success(response_data, time_cost_ms)

    except ValueError as ve:
        # 捕获已知的业务逻辑错误，例如空列表输入
        app_logger.error(f"Validation error for business_id: {request.business_id}. Error: {ve}", exc_info=True)
        return make_fail(400, f"请求参数错误: {ve}")
    except Exception as e:
        # 捕获所有其他未知异常
        app_logger.error(f"An unexpected error occurred for business_id: {request.business_id}. Error: {e}",
                         exc_info=True)
        return make_fail(500, "服务器内部错误，无法生成报告")
