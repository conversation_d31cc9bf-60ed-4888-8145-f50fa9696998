import os
import time
from fastapi import APIRouter
from pydantic import BaseModel, Field
from models.result import make_fail, make_success
from models.protocol_vo import ProtocolFileRequest
from utils.oss_utils import download_file
from utils.protocol_utils import extract_structured_protocol
from logger.logger import app_logger
from oss2.exceptions import NoSuchKey
router = APIRouter(prefix="/protocols", tags=["Protocols"])


# 用于存储从OSS下载的.docx文件的本地文件夹
PROTOCOL_FILES_FOLDER = 'temp_protocols'
if not os.path.exists(PROTOCOL_FILES_FOLDER):
    os.makedirs(PROTOCOL_FILES_FOLDER)


@router.post('/extract-by-docx')
async def extract_structure_from_docx(request: ProtocolFileRequest):
    """
    从OSS上的.docx方案文件中解析出层级化JSON结构。
    """
    start_time = time.time()
    file_key = request.protocol_file.file_key  # 提前获取 file_key 用于日志记录
    # 从请求中获取新增的控制参数
    extract_keys_only = request.extract_keys_only
    try:
        safe_local_filename = os.path.basename(file_key)
        if not safe_local_filename:
            return make_fail(400, f"Invalid file_key: '{file_key}' does not contain a file name.")

        local_file_path = os.path.join(PROTOCOL_FILES_FOLDER, safe_local_filename)

        app_logger.info(f"开始从OSS下载文件: {file_key} 到 {local_file_path}")
        download_file(file_key, local_file_path)
        app_logger.info("文件下载完成。")

        protocol_data = extract_structured_protocol(local_file_path, extract_keys_only=extract_keys_only)

        os.remove(local_file_path)

        duration = time.time() - start_time
        time_cost_ms = int(duration * 1000)

        app_logger.info(f"协议解析成功，耗时: {duration:.2f}s ({time_cost_ms}ms)")
        return make_success(protocol_data, time_cost_ms)

    # 专门捕获“文件未找到”的异常
    except NoSuchKey:
        # 使用安全的日志记录方式，不直接在f-string中格式化异常对象
        app_logger.warning(f"OSS文件未找到 (NoSuchKey)，请求的Key为: '{file_key}'")
        # 返回一个 404 Not Found 的友好提示
        return make_fail(404, f"File not found on OSS. Key: {file_key}")

    # 捕获所有其他未知异常
    except Exception as e:
        # 使用安全的日志记录方式
        app_logger.error(f"处理文件时发生未知错误, Key: '{file_key}'. 错误类型: {type(e).__name__}", exc_info=True)
        # 返回通用的 500 Internal Server Error
        return make_fail(500, "Internal Server Error")