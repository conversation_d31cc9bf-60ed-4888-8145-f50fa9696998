#!/usr/bin/env python
# encoding: utf-8

import os
from contextlib import asynccontextmanager

import httpx
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from aop.context import ContextMiddleware
from api.health import router as health_router
from api.pdf_parser import router as parser_router
from api.file_loader import router as file_loader_router
from api.ocr_api import router as ocr_router
from api.file_api import router as file_router
from api.doc_diff_api import router as doc_diff_router
from api.img2md import router as img2md_router
from api.med_api import router as med_write_router
from core import async_client  # 全局复用的 AsyncClient
from configurer.yy_nacos import Nacos
from logger.logger import app_logger


@asynccontextmanager
async def lifespan(app: FastAPI):
    # —— 启动阶段 —— #
    # 挂载全局 httpx client，供路由里直接使用
    app.state.async_client = async_client
    # 初始化 Nacos 客户端
    Nacos()
    yield  # ▶️ 应用开始接受请求
    # —— 关闭阶段 —— #
    await async_client.aclose()


def create_app() -> FastAPI:
    _app = FastAPI(lifespan=lifespan)

    # CORS 中间件
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _app.add_middleware(ContextMiddleware)

    # 挂载所有子路由
    router = APIRouter()
    router.include_router(health_router, include_in_schema=False)
    router.include_router(parser_router, include_in_schema=False)
    router.include_router(ocr_router, include_in_schema=False)
    router.include_router(file_router, include_in_schema=False)
    router.include_router(doc_diff_router, include_in_schema=False)
    router.include_router(file_loader_router, include_in_schema=False)
    router.include_router(img2md_router, include_in_schema=False)
    router.include_router(med_write_router, include_in_schema=False)

    _app.include_router(router)
    app_logger.info("路由已注册。")

    return _app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app:app",
        host="127.0.0.1",
        port=7860,
        reload=True,  # 开发时可开，生产请设为 False
        access_log=False,
    )
