#!/usr/bin/env python
# encoding: utf-8
from fastapi import FastAPI, APIRouter
from fastapi.middleware.cors import CORSMiddleware

from aop.context import ContextMiddleware
from api.health import router as health_router
from api.pdf_parser import router as parser_router
from api.file_loader import router as file_loader_router
from api.ocr_api import router as ocr_router
from api.file_api import router as file_router
from api.doc_diff_api import router as doc_diff_router
from api.img2md import router as img2md_router
from api.tfl_api import router as tfl_router
from api.protocol_api import router as protocol_router
from api.patient_rank_api import router as patient_rank_router
from configurer.yy_nacos import Nacos
from logger.logger import app_logger


def create_app():
    _app = FastAPI()

    origins = [
        "*",
    ]

    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _app.add_middleware(ContextMiddleware)

    # API routers
    router = APIRouter()
    router.include_router(health_router, include_in_schema=False)
    router.include_router(parser_router, include_in_schema=False)
    router.include_router(ocr_router, include_in_schema=False)
    router.include_router(file_router, include_in_schema=False)
    router.include_router(doc_diff_router, include_in_schema=False)
    router.include_router(file_loader_router, include_in_schema=False)
    router.include_router(img2md_router, include_in_schema=False)
    router.include_router(tfl_router, include_in_schema=False)
    router.include_router(protocol_router, include_in_schema=False)
    router.include_router(patient_rank_router, include_in_schema=False)

    _app.include_router(router)
    _app.add_event_handler("startup", Nacos)

    app_logger.info("app created.")
    return _app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="127.0.0.1", port=7860, reload=True, access_log=False)
