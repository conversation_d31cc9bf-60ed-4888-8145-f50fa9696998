import json
import os
import time
from typing import Dict

import nacos

from configurer.singleton import singleton
from logger.logger import app_logger

init_configs = {
    "lamma_parse_config": "{}",
    "asr_config": "{}",
    "yiya-gateway-config": "{}",
    "llm-vl-config": "{}",
    "med-write-config": "{}",
}

config: Dict[str, Dict] = {}


def watcher(args):
    data_id = args['data_id']
    content = args['content']
    try:
        data = json.loads(content)
        config[data_id] = data
        app_logger.info(f"配置变更：{data_id} => {content}")
    except Exception as e:
        app_logger.error(f"配置解析异常发生错误:{data_id} => {content}, {e}")
        app_logger.error(f"详细信息: {e.args}")


@singleton
class Nacos:
    def __init__(self):
        self.group_id = ""
        self.client = nacos.NacosClient(
            server_addresses=os.environ.get('NACOS_SERVER', 'mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848'),
            namespace=os.environ.get('NACOS_NAMESPACE', 'yy-prod'),
            ak="LTAI5tSMtzYQ5GVPCm8njDYp",
            sk="******************************"
        )

        for data_id, default_content in init_configs.items():
            data = self.client.get_config(data_id, self.group_id)
            if data:
                config[data_id] = json.loads(data)
            else:
                data = self.client.get_config(data_id, "DEFAULT_CONFIG")
                if data:
                    config[data_id] = json.loads(data)
                else:
                    config[data_id] = json.loads(default_content)
            self.client.add_config_watcher(data_id, self.group_id, watcher)


if __name__ == '__main__':
    mse = Nacos()
    d = config.get('lamma_parse_config')
    asr = config.get('asr_config')
    dd = config.get('yiya-gateway-config')
    vl = config.get('llm-vl-config')
    # 添加med-write-configer
    med_write_config = config.get('med-write-config')
    print(d)
    print(asr)
    print(dd)
    print(vl)
    print('med_write_config',med_write_config)
    time.sleep(1000)
