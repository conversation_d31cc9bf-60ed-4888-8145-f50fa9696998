SECTION_KEYWORD = "不良事件"
# 排除“严重不良事件”本身，作为结束条件
EXCLUDE_KEYWORD = "严重不良事件"
# 要匹配的关键词
SECTION_KEYWORD1 = "严重不良事件"
# 排除“严重不良事件”本身，作为结束条件
EXCLUDE_KEYWORD1 = "器械缺陷"
# 最大上传的文件大小
MAX_FILE_SIZE = 20 * 1024 * 1024  # 10MB
# --- 设置路径 & 映射:捷通 ---
REPORT_PLACEHOLDERS_MAPPING = {
    "试验名称": "试验名称",
    "申办单位": "申办方",
    "研究目的": "试验目的",
    "研究设计": "试验设计",
    "试验医疗器械": "试验医疗器械",
    "对照医疗器械": "对照医疗器械",
    "试验分组": "试验分组",
    "适用范围": "试验范围",
    "病例选择标准": "受试者选择",
    "主要研究结果": "主要有效终点",
}
# --- 设置路径 & 映射:fosun ---
REPORT_PLACEHOLDERS_MAPPING_FOSUN = {
    "临床研究标题": "方案标题",
    "药物名称": "研究药物",
    "研究方案编号": "方案编号",
    "临床研究分期": "临床研究分期",
    "产品名称摘要": "研究药物",
    "活性成分名称摘要": "研究药物",
    "研究标题摘要": "方案标题",
    "研究分期摘要": "临床研究分期",
    "研究目的摘要": "研究目的",
    "研究设计摘要": "研究设计",
    "样本量摘要": "样本量",
    "入选标准摘要": "入选标准",
    "排除标准摘要": "排除标准",
    "研究药物和用法用量摘要": "研究药物和用法用量",
    "安全性评价摘要": "安全性评价",
    "药物代谢动力学评价摘要": "药物代谢动力学评价",
    "疗效评价摘要": "疗效评价",
    "群体药代动力学摘要": "群体药代动力学及暴露-效应（E-R）关系评价",
    "生物标志物评价摘要": "生物标志物评价",
    "分析人群摘要": "分析人群",
    "疗效分析摘要": "疗效分析",
    "安全性分析摘要": "安全性分析",
    "药代动力学分析摘要": "药代动力学分析",
}
# 通用的摘要生成函数
PROMPT_CFG = {
    "既往/伴随疾病": ("prompt_template3.py", "{{既往伴随疾病表名称}}", "{{既往伴随疾病表格}}"),
    "手术史": ("prompt_template4.py", "{{手术史表名称}}", "{{手术史表格}}"),
    "过敏史": ("prompt_template5.py", "{{过敏史表名称}}", "{{过敏史表格}}"),
    "合并用药": ("prompt_template6.py", "{{合并用药表名称}}", "{{合并用药表格}}"),
    "合并非药物治疗": ("prompt_template7.py", "{{合并非药物治疗表名称}}", "{{合并非药物治疗表格}}"),
    "受试眼选择": ("prompt_template8.py", "{{受试眼选择表名称}}", "{{受试眼选择表格}}"),
    "重要方案偏离情况": ("prompt_template20.py", "{{临床试验方案的偏离表格1}}", "{{临床试验方案的偏离附注1}}"),

}
mappings = {
    '分析人群': '分析数据集',
    '统计分析一般原则': '统计学分析原则',
    '基线分析': '基线分析',
    '疗效分析': '疗效分析',
    '主要有效性评价': '主要有效性评价',
    '次要有效性评价': '次要有效性评价',
    '安全性评价指标分析': '安全性指标',
    '缺失值和异常值的处理': '缺失值和异常值的处理',
}