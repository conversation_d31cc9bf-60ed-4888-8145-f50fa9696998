from docx.table import Table
from docx.shared import Pt
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from docx import Document
import json
import os
import re
import logging
import zipfile
from xml.etree import ElementTree as ET

logger = logging.getLogger(name=__name__)

from med_write.utils import *
from med_write.utils import HEADING_PATTERN_pop
from configurer.yy_nacos import Nacos
from configurer.config_reader import get_med_write_config


class Extractor:
    """
    封装所有基于正则和 python-docx 的提取逻辑
    """

    def __init__(self) -> None:
        # 初始化 clint
        self.client = get_openai_client()

    def extract_first_page_text(self, docx_path, max_chars=500):
        doc = Document(docx_path)
        text_parts, count = [], 0
        for p in doc.paragraphs:
            if count >= max_chars:
                break
            text_parts.append(p.text)
            count += len(p.text)
        return "\n".join(text_parts)[:max_chars]

    def extract_first_page_text_fosun(self, docx_path, max_chars=500):
        """
        只提取 docx 文档中的第一个表格，并转换为 Markdown 格式
        """
        doc = Document(docx_path)
        # 如果没有表格，返回提示
        if not doc.tables:
            return "【未找到表格】"
        # 取第一个表格
        first_table = doc.tables[0]
        # 转为 Markdown 并返回
        if not first_table:
            return "【未找到表格】"
        lines = [" | ".join(cell.text.strip() for cell in row.cells) for row in first_table.rows]
        markdown_text = "\n".join(lines)
        return markdown_text

    def extract_protocol_metadata(self, docx_path):
        # 1) 读前500字
        snippet = self.extract_first_page_text(docx_path)
        # 2) 拼 prompt
        prompt = f"""请你扮演一个医学写作助手。下面是临床试验方案文档的前500字（用"<<<"和">>>"括起来）。请从中提取以下字段，并以合法 JSON（**只输出 JSON，不要出现任何 ``` 或 Markdown 代码块标记**）返回，不要多余任何说明文字：
- report_id：方案编号  
- clinical_trial_name：试验名称  
- trial_medical_device_name：试验医疗器械名称  
- device_model_specification：临床试验使用的型号规格  
- clinical_trial_institution：临床试验机构  
- principal_investigator：主要研究者  
- protocol_version_date：方案版本号和日期  
- sponsor：申办单位  

如果文中未找到某个字段，请将其值设为 null。

<<<
{snippet}
>>>/no_think
    """
        # 3) 调用模型
        mse = Nacos()
        config = get_med_write_config()
        MODEL_NAME = config.get("MODEL_NAME")
        resp = self.client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            max_tokens=512
        )
        content = resp.choices[0].message.content.strip()

        # 4) 清理可能的 Markdown 代码块标记
        import re
        # 移除可能的 ```json 和 ``` 标记
        content = re.sub(r'^```json\s*', '', content, flags=re.MULTILINE)
        content = re.sub(r'\s*```$', '', content, flags=re.MULTILINE)
        content = content.strip()

        # 5) 解析 JSON
        try:
            data = json.loads(content)
            logger.info(f"成功解析元数据: {data}")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败，原始内容：\n{repr(content)}")
            logger.error(f"JSON解析错误详情：{e}")
            raise ValueError(f"无法解析 JSON，错误：{e}\n原始内容：\n{content}")

    def extract_protocol_metadata_fosun(self, docx_path):
        # 1) 读前500字
        snippet = self.extract_first_page_text_fosun(docx_path)
        # 2) 拼 prompt
        prompt = f"""请你扮演一个医学写作助手。下面是临床试验方案文档的前500字（用"<<<"和">>>"括起来）。请从中提取以下字段，并以合法 JSON（**只输出 JSON，不要出现任何 ``` 或 Markdown 代码块标记**）返回，不要多余任何说明文字：

    - sponsor：申办方 
    - sponsor_contact：申办方联系人
    - sponsor_contact_number：申办方联系方式
    
    注意：
    - 如果文中未找到某个字段，请将其值设为 null。
    - 申办方为公司名称，联系人和联系方式为公司名称的负责人或者联系人。
    <<<
    {snippet}
    >>>/no_think
        """
        # 3) 调用模型
        mse = Nacos()
        config = get_med_write_config()
        MODEL_NAME = config.get("MODEL_NAME")
        resp = self.client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0,
            max_tokens=512
        )
        content = resp.choices[0].message.content.strip()

        # 4) 清理可能的 Markdown 代码块标记
        import re
        # 移除可能的 ```json 和 ``` 标记
        content = re.sub(r'^```json\s*', '', content, flags=re.MULTILINE)
        content = re.sub(r'\s*```$', '', content, flags=re.MULTILINE)
        content = content.strip()

        # 5) 解析 JSON
        try:
            data = json.loads(content)
            logger.info(f"成功解析元数据: {data}")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败，原始内容：\n{repr(content)}")
            logger.error(f"JSON解析错误详情：{e}")
            raise ValueError(f"无法解析 JSON，错误：{e}\n原始内容：\n{content}")

    # 通用节提取函数
    def extract_section(self, docx_path: str, title: str) -> str:
        """
        提取文档中指定 title 的章节内容，直到下一个编号章节开始为止。
        """
        heading_regex = re.compile(rf'^\s*{NUM_PATTERN}{re.escape(title)}\s*$', re.IGNORECASE)
        plain_regex = re.compile(rf'^\s*{re.escape(title)}\s*$', re.IGNORECASE)
        next_sec_regex = re.compile(rf'^\s*{NUM_PATTERN}\S+')
        doc = Document(docx_path)
        in_section = False
        lines = []
        for para in doc.paragraphs:
            txt = para.text.strip()
            if not in_section:
                if heading_regex.match(txt) or plain_regex.match(txt):
                    in_section = True
                continue
            if next_sec_regex.match(txt):
                break
            if txt:
                lines.append(txt)
        return "\n".join(lines).strip()

    def extract_section_fusun(self, docx_path: str, title: str) -> str:
        """
        提取文档中指定 title 的章节内容，直到遇到下一个标题为止。

        参数:
            docx_path: Word文档路径
            title: 要提取的章节标题

        返回:
            提取到的章节内容字符串
        """
        # 编译匹配带编号标题的正则表达式（如"1.2 医学背景"）
        heading_regex = re.compile(rf'^\s*{NUM_PATTERN}{re.escape(title)}\s*$', re.IGNORECASE)

        # 编译匹配无编号纯标题的正则表达式（如"医学背景"）
        plain_regex = re.compile(rf'^\s*{re.escape(title)}\s*$', re.IGNORECASE)

        # 读取Word文档
        doc = Document(docx_path)

        in_section = False  # 是否已找到目标章节
        lines = []  # 存储提取的内容

        def is_heading_style(style_name):
            """判断是否为标题样式"""
            return style_name in ['Heading 1', 'Heading 2', 'Heading 3', 'Heading 4', 'Heading 5', 'Heading 6',
                                  '标题 1', '标题 2', '标题 3', '标题 4', '标题 5', '标题 6']

        for para in doc.paragraphs:
            txt = para.text.strip()

            if not in_section:
                # 检查是否匹配目标标题
                if (heading_regex.match(txt) or plain_regex.match(txt)) and is_heading_style(para.style.name):
                    in_section = True
                    # 不包含标题本身，只提取正文内容
                    continue
            else:
                # 已经在目标章节中
                if is_heading_style(para.style.name):
                    # 遇到任何标题，停止提取
                    break
                else:
                    # 普通正文段落，收集内容
                    if txt:
                        lines.append(txt)

        result = "\n".join(lines).strip()

        return result

    def extract_section_raw_xml(self, docx_path: str, title: str) -> str:
        """
        直接从docx文件中提取原始XML内容，并去除软回车，同时重映射编号ID为无序列表
        """
        heading_regex = re.compile(rf'^\s*{NUM_PATTERN}{re.escape(title)}\s*$', re.IGNORECASE)
        plain_regex = re.compile(rf'^\s*{re.escape(title)}\s*$', re.IGNORECASE)
        next_sec_regex = re.compile(rf'^\s*{NUM_PATTERN}\S+')

        # 打开docx文件（实际上是zip文件）
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            # 读取主文档XML
            document_xml = docx_zip.read('word/document.xml')

        # 解析XML
        root = ET.fromstring(document_xml)

        # 定义命名空间 - 注意这里用的是w，但XML中实际是ns0
        namespaces = {
            'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
        }

        in_section = False
        section_elements = []

        # 遍历所有段落
        for para in root.findall('.//w:p', namespaces):
            # 获取段落文本
            para_text = ''.join(para.itertext()).strip()

            if not in_section:
                if heading_regex.match(para_text) or plain_regex.match(para_text):
                    in_section = True
                continue

            if next_sec_regex.match(para_text):
                break

            if para_text:
                # 创建段落副本以避免修改原始XML
                para_copy = ET.fromstring(ET.tostring(para, encoding='unicode'))

                # 去除软回车标签
                self._remove_soft_breaks(para_copy, namespaces)

                # 获取段落的XML字符串并重映射编号ID
                para_xml = ET.tostring(para_copy, encoding='unicode')
                para_xml = self._remap_numbering_ids_string(para_xml)

                # 保存处理后的段落XML字符串
                section_elements.append(para_xml)

        return ''.join(section_elements)

    def _remove_soft_breaks(self, element, namespaces):
        """
        移除 XML 元素中的软回车，包括Word中^l能匹配的所有字符：
        - <w:br/> 标签（手动换行符）
        - w:t 元素内部的各种换行字符
        """
        # 1. 移除所有类型的 <w:br/> 元素
        for br in element.findall('.//w:br', namespaces):
            parent = br.getparent()
            if parent is not None:
                parent.remove(br)

        # 2. 清理 w:t 中的所有软换行字符（Word ^l 对应的字符）
        for t in element.findall('.//w:t', namespaces):
            if t.text:
                # Word中^l能匹配的所有字符
                t.text = (
                    t.text
                    .replace('\n', '')  # 换行符 (LF) - Unicode U+000A
                    .replace('\r', '')  # 回车符 (CR) - Unicode U+000D
                    .replace('\u000B', '')  # 垂直制表符 (VT) - Unicode U+000B
                    .replace('\u000C', '')  # 换页符 (FF) - Unicode U+000C
                    .replace('\u0085', '')  # 下一行 (NEL) - Unicode U+0085
                    .replace('\u2028', '')  # 行分隔符 (Line Separator) - Unicode U+2028
                    .replace('\u2029', '')  # 段落分隔符 (Paragraph Separator) - Unicode U+2029
                    .replace('\u000D\u000A', '')  # CRLF组合 (先处理组合，再处理单个)
                )

                # 如果还有其他不可见字符，可以使用正则表达式一次性清理
                import re
                # 清理所有控制字符（除了制表符\t和空格）
                t.text = re.sub(r'[\x00-\x08\x0A-\x1F\x7F-\x9F\u2028\u2029]', '', t.text)

    def extract_text_from_cell(self, cell):
        """从单元格中提取所有段落的文本并合并"""
        return "\n".join([p.text for p in cell.paragraphs]).strip()

    def find_protocol_summary_table_and_extract_data(self, docx_filepath, chapter_name="方案摘要"):
        """
        1“方案摘要”的表格直接提取
        """
        try:
            document = Document(docx_filepath)
            summary_data = {}
            found_summary_heading = False

            # 遍历文档中的所有元素（段落和表格）
            # doc.element.body直接访问lxml元素能更可靠地按顺序处理
            for element in document.element.body:
                # 如果是段落 (CT_P)
                if element.tag.endswith('p'):
                    # 将lxml段落元素包装成python-docx的Paragraph对象以便获取文本
                    paras_with_element = [p for p in document.paragraphs if p._element is element]
                    if not paras_with_element:
                        continue
                    para = paras_with_element[0]

                    if chapter_name in para.text:
                        found_summary_heading = True
                        continue

                # 如果是表格 (CT_Tbl) 并且已经找到了 "方案摘要" 标题
                if found_summary_heading and element.tag.endswith('tbl'):
                    tables_with_element = [t for t in document.tables if t._element is element]
                    if not tables_with_element:
                        continue
                    table = tables_with_element[0]

                    # 检查表格是否符合预期的两列结构
                    if not table.rows or len(table.columns) < 2:
                        continue

                    for row_idx, row in enumerate(table.rows):
                        if len(row.cells) >= 2:
                            key_cell_text = self.extract_text_from_cell(row.cells[0])
                            value_cell_text = self.extract_text_from_cell(row.cells[1])

                            # 清理键名，去除可能的星号和冒号
                            key_cleaned = key_cell_text.replace('*', '') \
                                .replace(' ', '') \
                                .replace('：', '') \
                                .replace(':', '') \
                                .strip()

                            if key_cleaned:
                                summary_data[key_cleaned] = value_cell_text
                                logger.info(
                                    f"  提取到: '{key_cleaned}' -> '{value_cell_text[:50].replace(chr(10), ' ')}...'")
                            elif row_idx == 0 and not key_cleaned and not value_cell_text:
                                logger.info("  信息: 表格第一行为空，已跳过。")
                            elif not key_cleaned and value_cell_text:
                                logger.info(
                                    f"  警告: 第 {row_idx + 1} 行键为空，但值存在: '{value_cell_text[:50].replace(chr(10), ' ')}...'")
                        else:
                            logger.info(
                                f"  警告: 表格行 '{self.extract_text_from_cell(row.cells[0]) if row.cells else '空行'}' 不足两列，已跳过。")

                    # 假设方案摘要的表格是找到的第一个符合条件的表格
                    if summary_data:
                        return summary_data
                    else:
                        logger.info("警告：解析了表格，但未提取到有效数据。继续寻找...")
                        found_summary_heading = False

            if not summary_data:
                logger.info("错误：遍历完文档也未找到有效的'方案摘要'表格或未能从中提取数据。")
                if not found_summary_heading:
                    logger.info("提示：未找到任何包含'方案摘要'字样的段落作为起始点。")
            return summary_data

        except FileNotFoundError:
            logger.info(f"错误：输入文件 '{docx_filepath}' 未找到。请检查路径是否正确。")
            return None
        except Exception as e:
            logger.info(f"处理文件 '{docx_filepath}' 时发生错误: {e}")
            return None

    def extract_research_background(self, docx_path: str) -> str:
        """
        2临床试验的背景
        """
        doc = Document(docx_path)
        in_section = False
        lines = []

        for para in doc.paragraphs:
            text = para.text.strip()
            # 如果尚未进入目标小节，先检测标题
            if not in_section:
                if BACKGROUND_HEADING_PATTERN.match(text):
                    in_section = True
                    # 不把标题本身也算入正文
                    continue
            else:
                # 一旦碰到下一个同级或更高级章节，就结束提取
                if NEXT_SECTION_PATTERN.match(text):
                    break
                # 非空段落才加入
                if text:
                    lines.append(text)

        return "\n".join(lines).strip()

    def extract_trial_objective(self, docx_path: str) -> str:
        '''
        3 试验目的部分提取
        '''
        doc = Document(docx_path)
        in_section = False
        lines = []
        for para in doc.paragraphs:
            text = para.text.strip()
            if not in_section:
                if OBJECTIVE_HEADING_PATTERN.match(text) or PLAIN_OBJECTIVE_HEADING.match(text):
                    in_section = True
                continue
            # 进入目标节后，遇到下一个章节号就退出
            if NEXT_SECTION_PATTERN.match(text):
                break
            if text:
                lines.append(text)
        return "\n".join(lines).strip()

    def extract_trial_flowchart(self, docx_path: str) -> str:
        '''
        4试验流程图
        '''
        doc = Document(docx_path)
        in_section = False
        lines = []
        for para in doc.paragraphs:
            txt = para.text.strip()
            if not in_section:
                if FLOW_HEADING_PATTERN.match(txt) or PLAIN_FLOW_HEADING.match(txt):
                    in_section = True
                continue
            if NEXT_SECTION_PATTERN.match(txt):
                break
            if txt:
                lines.append(txt)
        return "\n".join(lines).strip()

    def extract_trial_flow_table(self, docx_path: str) -> Table:
        '''
        4试验流程图
        '''
        doc = Document(docx_path)
        body = doc.element.body
        in_section = False

        for child in body.iterchildren():
            tag = child.tag.lower()
            # 段落：检查是否进入或离开目标节
            if tag.endswith('}p'):
                # 提取这一段的所有文本
                txt = ''.join(node.text or ""
                              for node in child.iter()
                              if node.tag.lower().endswith('}t')).strip()
                if not in_section:
                    if FLOW_HEADING_PATTERN.match(txt) or PLAIN_FLOW_HEADING.match(txt):
                        in_section = True
                else:
                    if NEXT_SECTION_PATTERN.match(txt):
                        # 已到下节，退出
                        break
            # 表格：若已在节内，返回第一个表格
            elif tag.endswith('}tbl') and in_section:
                return Table(child, doc)

        return None

    def extract_table_and_note(self, doc: Document):

        body = doc.element.body
        in_section = False
        title = note = None
        table = None
        for child in list(body.iterchildren()):
            tag = child.tag.lower()
            if tag.endswith('}p'):
                text = ''.join(n.text or '' for n in child.iter() if n.tag.lower().endswith('}t')).strip()
                if not in_section and HEADING_PATTERN_pop.match(text):
                    in_section = True
                    title = text
                elif in_section:
                    if NEXT_SECTION_PATTERN_pop.match(text) and '人口统计学资料' not in text:
                        break
                    if NOTE_PATTERN.match(text):
                        note = text
            elif in_section and tag.endswith('}tbl') and table is None:
                table = Table(child, doc)
        return title or '【未找到人口统计学资料标题】', table, note or '【未找到附注】'

    def extract_table_and_note1(self, doc: Document, target_title: str, start_idx: int = 0):
        '''
        # 六、临床试验结果（一）分析人群,是因为需要提取多个表格，所以才有current_idx吧？怎么样和extract_table_and_note合并下
        '''
        body = doc.element.body
        in_section = False
        title = None
        table = None
        note = None
        current_idx = 0
        for child in list(body.iterchildren())[start_idx:]:
            tag = child.tag.lower()
            if tag.endswith('}p'):
                txt = ''.join(node.text or "" for node in child.iter() if node.tag.lower().endswith('}t')).strip()
                if not in_section:
                    if HEADING_PATTERN_372.match(txt) and target_title in txt:
                        in_section = True
                        title = txt
                        continue
                else:
                    if NEXT_SECTION_PATTERN376.match(txt):
                        break
                    if NOTE_PATTERN.match(txt):
                        note = txt
                        continue
            elif tag.endswith('}tbl') and in_section and not table:
                table = Table(child, doc)
            current_idx += 1

        return title, table, note, start_idx + current_idx

    def extract_all_sections(self, doc: Document, section_name: str):
        """
        扫一遍 body，把每个匹配到的标题 + 紧随其后的 tables / note 都抓成一个 dict，
        返回列表 [ {title, tables[], note}, ... ]
        """
        body = doc.element.body

        heading_re = make_heading_pattern1(section_name)
        sections, curr = [], None
        for child in body.iterchildren():
            tag = child.tag.lower()
            # 段落
            if tag.endswith('}p'):
                text = ''.join(n.text or '' for n in child.iter() if n.tag.lower().endswith('}t')).strip()

                # 新标题
                if heading_re.match(text):
                    if curr:
                        sections.append(curr)
                    curr = {'title': text, 'tables': [], 'note': ""}
                    continue

                # 当前 section 内，检测下一章节或注释
                if curr:
                    if NEXT_SECTION_PATTERN2.match(text) and section_name not in text:
                        sections.append(curr)
                        curr = None
                        continue
                    if NOTE_PATTERN.match(text):
                        curr['note'] = text
                        continue

            # 表格节点
            if curr and tag.endswith('}tbl'):
                curr['tables'].append(Table(child, doc))

        if curr:
            sections.append(curr)
        return sections

    def extract_tables_and_note(self, doc: Document, section_name: str):
        body = doc.element.body
        in_section = False
        title = None
        tables = []
        note = None
        heading_re = make_heading_pattern(section_name)

        for child in list(body.iterchildren()):
            tag = child.tag.lower()
            if tag.endswith('}p'):
                text = ''.join(n.text or '' for n in child.iter() if n.tag.lower().endswith('}t')).strip()
                if not in_section and heading_re.match(text):
                    in_section = True
                    title = text
                elif in_section:
                    # 遇到下一个表格开头且不是本章节时，结束
                    if NEXT_SECTION_PATTERN2.match(text) and section_name not in text:
                        break
                    # 匹配注释
                    if NOTE_PATTERN.match(text):
                        note = text
            elif in_section and tag.endswith('}tbl'):
                tables.append(Table(child, doc))

        return (
            title or f'【未找到{section_name}标题】',
            tables,
            note or '【未找到附注】'
        )

    def extract_tables_and_note_abbr(self, doc: Document, section_name: str):
        body = doc.element.body
        in_section = False
        title = None
        tables = []
        heading_re = make_heading_pattern_abbr(section_name)

        for child in list(body.iterchildren()):
            tag = child.tag.lower()
            if tag.endswith('}p'):
                text = ''.join(n.text or '' for n in child.iter() if n.tag.lower().endswith('}t')).strip()
                if not in_section and heading_re.match(text):
                    in_section = True
                    title = text
                elif in_section:
                    # 遇到下一个章节开头且不是本章节时，结束
                    if NEXT_SECTION_PATTERN_abbr.match(text) and section_name not in text:
                        break

            elif in_section and tag.endswith('}tbl'):
                tables.append(Table(child, doc))

        return (
            title or f'【未找到{section_name}标题】',
            tables
        )

    def extract_section_by_heading(self, docx_path: str, keyword: str, exclude: str) -> str:
        """
        根据 Heading 样式，提取包含 keyword（但不含 exclude）的那一节正文，
        并自动剥离每行开头的数字编号。
        """
        doc = Document(docx_path)
        in_section = False
        orig_level = None
        lines = []

        for para in doc.paragraphs:
            text = para.text.strip()
            if not text:
                continue

            style = getattr(para.style, "name", "")

            if not in_section:
                # 找到一级：Heading 且包含 keyword，但不能是 exclude
                if style.startswith("Heading") and keyword in text and exclude not in text:
                    in_section = True
                    try:
                        orig_level = int(style.split()[-1])
                    except ValueError:
                        orig_level = None
                    continue

            else:
                # 若遇到下一个同级或更高级的 Heading，则结束
                if style.startswith("Heading"):
                    try:
                        lvl = int(style.split()[-1])
                    except ValueError:
                        lvl = None
                    if orig_level is None or (lvl is not None and lvl <= orig_level):
                        break

                # 剥离行首编号，再加入
                cleaned = LEADING_NUM_PATTERN.sub("", text)
                if cleaned:
                    lines.append(cleaned)

        return "\n".join(lines).strip()

    def extract_section_by_heading2(self, docx_path: str, keyword: str, exclude: str) -> str:
        """
        根据 Heading2 样式，提取包含 keyword（但不含 exclude）的那一节正文，
        并自动剥离每行开头的数字编号，然后重新编号。
        """
        doc = Document(docx_path)
        in_section = False
        lines = []

        for para in doc.paragraphs:
            text = para.text.strip()
            if not text:
                continue

            style = getattr(para.style, "name", "")

            if not in_section:
                # 只匹配 Heading 2 且包含 keyword，但不能是 exclude
                if style == "Heading 2" and keyword in text and exclude not in text:
                    in_section = True
                    continue

            else:
                # 若遇到下一个 Heading 2，则结束
                if style == "Heading 2":
                    break

                # 剥离行首编号，再加入
                cleaned = LEADING_NUM_PATTERN.sub("", text)
                if cleaned:
                    lines.append(cleaned)

        # 重新编号处理
        result_lines = []
        counter = 1

        for line in lines:
            # 检查是否以"）"开头，如果是则需要重新编号
            if line.startswith("）"):
                # 去掉开头的"）"，然后添加新的编号
                content = line[1:]  # 去掉第一个字符"）"
                new_line = f"（{counter}）{content}"
                result_lines.append(new_line)
                counter += 1
            else:
                result_lines.append(line)

        return "\n".join(result_lines).strip()

    def extract_sections_by_prefix(self, doc: Document):
        """
        扫一遍 body，把每个以“表9.1.x.x”开头的标题 + 后续表格/注释抓成 dict 列表
        返回：[{ 'title': str, 'tables': [Table,...], 'note': str|None }, ...]
        """
        # 正则：匹配 “表9.1.x.x” 开头的标题
        sections, curr = [], None
        for child in doc.element.body.iterchildren():
            tag = child.tag.lower()
            # 段落
            if tag.endswith('}p'):
                text = ''.join(n.text or '' for n in child.iter() if n.tag.lower().endswith('}t')).strip()
                # 新标题
                if HEADING_PATTERN677.match(text):
                    if curr:
                        sections.append(curr)
                    curr = {'title': text, 'tables': [], 'note': None}
                    continue
                # 当前节内：遇到下一个任意表就结束本节；遇到注释就记录
                if curr:
                    if NEXT_HEADING_PATTERN.match(text) and not HEADING_PATTERN677.match(text):
                        sections.append(curr)
                        curr = None
                        continue
                    if NOTE_PATTERN.match(text):
                        curr['note'] = text
                        continue

            # 表格节点
            if curr and tag.endswith('}tbl'):
                curr['tables'].append(Table(child, doc))

        if curr:
            sections.append(curr)
        return sections

    def extract_tables_by_cell_content(self, doc: Document, target_content: str = "********"):
        """
        遍历文档中的所有表格，找到第一行第一列包含指定内容的表格
        提取第一行第一列作为完整标题（如"表******** 试验筛选情况-所有筛选受试者"），
        将标题中的数字部分替换为"X"（结果形如"表X 试验筛选情况-所有筛选受试者"），
        删除第一行后处理"数据来源"行并返回表格，同时额外返回该表格的 Markdown 格式内容

        Args:
            doc: Document 对象
            target_content: 要查找的内容，默认为 "********"

        Returns:
            tuple: (title: str, tables: list[Table], markdown_tables: list[str])
                - title: 修改后的标题（如 "表X 试验筛选情况-所有筛选受试者"）
                - tables: 处理完毕的 docx.table.Table 对象列表
                - markdown_tables: 对应每张 table 的 Markdown 文本列表
        """
        body = doc.element.body
        target_tables = []
        markdown_tables = []
        title = ""

        for child in list(body.iterchildren()):
            tag = child.tag.lower()

            if tag.endswith('}tbl'):
                table = Table(child, doc)

                if len(table.rows) > 0 and len(table.rows[0].cells) > 0:
                    first_cell_text = table.rows[0].cells[0].text.strip()

                    if target_content in first_cell_text:
                        # 记录完整原始标题
                        if not title:
                            original_title = first_cell_text  # e.g. "表******** 试验筛选情况-所有筛选受试者"
                            m = re.match(r"^(表)\d+(?:\.\d+)*\s*(.*)$", original_title)
                            if m:
                                suffix = m.group(2)  # "试验筛选情况-所有筛选受试者"
                                title = f"表X {suffix}"
                            else:
                                title = "表X"

                        # 从完整标题中提取编号（纯数字和点），用于"数据来源"行的替换
                        m_id = re.search(r"(\d+(?:\.\d+)*)", first_cell_text)
                        table_id = m_id.group(1) if m_id else target_content  # e.g. "********"

                        # 删除第一行
                        self._remove_first_row(table)

                        # 删除最后一列合计数为0或1的行，但是只有出现 14.3.2.2的时候才会删除
                        if target_content == "14.3.2.2":
                            self._remove_rows_with_low_total(table)

                        # 处理最后一行中可能包含"数据来源："
                        if len(table.rows) > 0:
                            last_row = table.rows[-1]
                            for cell in last_row.cells:
                                cell_text = cell.text
                                if "数据来源：" in cell_text:
                                    idx = cell_text.find("数据来源：")
                                    prefix = cell_text[:idx].rstrip()
                                    # 拼接：保留前面内容（如有）+ 换行 + "数据来源：表{编号}"
                                    if prefix:
                                        new_text = prefix + "\n" + f"数据来源：表{table_id}"
                                    else:
                                        new_text = f"数据来源：表{table_id}"
                                    cell.text = new_text
                                    break

                        # 设置表格样式：添加上下边框并左对齐
                        self._format_table(table)

                        # 将处理后的 table 转为 Markdown 文本（去掉最后一行）
                        lines = []
                        # 判断是否需要去掉最后一行：如果表格有多行，则去掉最后一行
                        rows_to_process = table.rows[:-1] if len(table.rows) > 1 else table.rows

                        for row in rows_to_process:
                            row_texts = [cell.text.strip() for cell in row.cells]
                            lines.append(" | ".join(row_texts))
                        markdown = "\n".join(lines)

                        target_tables.append(table)
                        markdown_tables.append(markdown)

        return title, target_tables, markdown_tables

    def _remove_first_row(self, table):
        """
        删除表格的第一行

        Args:
            table: Table对象
        """
        if len(table.rows) > 0:
            # 获取第一行的XML元素
            first_row = table.rows[0]._tr
            # 从父元素中移除第一行
            first_row.getparent().remove(first_row)

    def _format_table(self, table):
        """
        设置表格格式：添加上下边框，设置文字左对齐
        """
        # 设置表格对齐方式
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 获取表格的XML元素
        tbl = table._tbl

        # 设置表格边框
        tblPr = tbl.tblPr
        if tblPr is None:
            tblPr = OxmlElement('w:tblPr')
            tbl.insert(0, tblPr)

        # 创建表格边框元素
        tblBorders = tblPr.find(qn('w:tblBorders'))
        if tblBorders is None:
            tblBorders = OxmlElement('w:tblBorders')
            tblPr.append(tblBorders)

        # 设置上边框
        top_border = OxmlElement('w:top')
        top_border.set(qn('w:val'), 'single')
        top_border.set(qn('w:sz'), '4')  # 边框粗细
        top_border.set(qn('w:space'), '0')
        top_border.set(qn('w:color'), '000000')  # 黑色
        tblBorders.append(top_border)

        # 设置下边框
        bottom_border = OxmlElement('w:bottom')
        bottom_border.set(qn('w:val'), 'single')
        bottom_border.set(qn('w:sz'), '4')  # 边框粗细
        bottom_border.set(qn('w:space'), '0')
        bottom_border.set(qn('w:color'), '000000')  # 黑色
        tblBorders.append(bottom_border)
        #
        # # 移除左右边框（如果存在）
        # left_border = tblBorders.find(qn('w:left'))
        # if left_border is not None:
        #     left_border.set(qn('w:val'), 'nil')
        #
        # right_border = tblBorders.find(qn('w:right'))
        # if right_border is not None:
        #     right_border.set(qn('w:val'), 'nil')
        #
        # # 移除内部边框
        # insideH = tblBorders.find(qn('w:insideH'))
        # if insideH is not None:
        #     insideH.set(qn('w:val'), 'nil')
        #
        # insideV = tblBorders.find(qn('w:insideV'))
        # if insideV is not None:
        #     insideV.set(qn('w:val'), 'nil')

        # 设置所有单元格文字左对齐
        for row in table.rows:
            for cell in row.cells:
                # 设置单元格内所有段落左对齐
                for paragraph in cell.paragraphs:
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                    # 可选：设置字体大小
                    for run in paragraph.runs:
                        run.font.size = Pt(8)  # 设置字体大小为10磅

    def extract_images_between_tables(self, docx_path, start_pattern, end_pattern):
        """
        提取两个表格之间的图片
        """
        images = []

        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            document_xml = docx_zip.read('word/document.xml')
            root = ET.fromstring(document_xml)

            namespaces = {
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
                'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
                'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture',
                'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
            }

            rels_xml = docx_zip.read('word/_rels/document.xml.rels')
            rels_root = ET.fromstring(rels_xml)

            rel_map = {}
            for rel in rels_root.findall('.//Relationship',
                                         {'': 'http://schemas.openxmlformats.org/package/2006/relationships'}):
                rel_map[rel.get('Id')] = rel.get('Target')

            body = root.find('.//w:body', namespaces)
            if body is None:
                return images

            collecting = False
            current_title = ""
            current_original_id = ""

            for element in body:
                if element.tag.endswith('}tbl'):
                    first_cell_text = self._get_first_cell_text(element, namespaces)

                    if first_cell_text:

                        # 检查是否是开始收集的表格
                        if start_pattern in first_cell_text:
                            collecting = True

                        # 检查是否是结束收集的表格
                        if end_pattern in first_cell_text and collecting:
                            break

                        # 如果正在收集，且这是一个图表（以"图"开头），则更新当前信息
                        if collecting and first_cell_text.startswith('图'):
                            import re
                            # 提取原始编号（如 "图14.2.1.1"）
                            m_id = re.search(r"(图\d+(?:\.\d+)*)", first_cell_text)
                            current_original_id = m_id.group(1) if m_id else ""

                            # 修改标题：将数字部分替换为 "x"
                            m = re.match(r"^(图)\d+(?:\.\d+)*\s*(.*)$", first_cell_text)
                            if m:
                                suffix = m.group(2)
                                current_title = f"图 x {suffix}"
                            else:
                                current_title = "图 x"


                elif collecting and element.tag.endswith('}p'):
                    paragraph_images = self._extract_images_from_paragraph(element, namespaces, docx_zip, rel_map)
                    for image_binary in paragraph_images:
                        images.append((current_title, current_original_id, image_binary))

        return images

    def _get_first_cell_text(self, table_element, namespaces):
        """
        获取表格第一行第一列的文本内容（私有方法）
        """
        try:
            first_row = table_element.find('.//w:tr', namespaces)
            if first_row is not None:
                first_cell = first_row.find('.//w:tc', namespaces)
                if first_cell is not None:
                    text_elements = first_cell.findall('.//w:t', namespaces)
                    return ''.join([t.text or '' for t in text_elements])
        except Exception:
            pass
        return ''

    def _extract_images_from_paragraph(self, paragraph, namespaces, docx_zip, rel_map):
        """
        从段落中提取图片二进制数据（私有方法）
        """
        images = []
        drawings = paragraph.findall('.//w:drawing', namespaces)

        for drawing in drawings:
            blips = drawing.findall('.//a:blip', namespaces)
            for blip in blips:
                embed_id = blip.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                if embed_id and embed_id in rel_map:
                    image_path = rel_map[embed_id]
                    if image_path.startswith('/'):
                        image_path = image_path[1:]
                    if not image_path.startswith('word/'):
                        image_path = 'word/' + image_path

                    try:
                        image_binary = docx_zip.read(image_path)
                        images.append(image_binary)
                    except KeyError:
                        continue

        return images

    def _remove_rows_with_low_total(self, table):
        """
        删除最后一列（合计列）数值为0或1的行

        Args:
            table: docx.table.Table 对象
        """
        import re

        # 从后往前遍历，避免删除行时索引变化的问题
        rows_to_remove = []

        for i in range(len(table.rows) - 1, -1, -1):
            row = table.rows[i]
            if len(row.cells) > 0:
                # 获取最后一列的文本
                last_cell_text = row.cells[-1].text.strip()

                # 使用正则表达式提取数字（匹配如 "1 (6.3)" 中的第一个数字）
                match = re.match(r'^(\d+)', last_cell_text)
                if match:
                    total_count = int(match.group(1))
                    # 如果合计数为0或1，标记为需要删除的行
                    if total_count <= 1:
                        rows_to_remove.append(i)

        # 删除标记的行
        for row_index in rows_to_remove:
            self._remove_row_by_index(table, row_index)

    def _remove_row_by_index(self, table, row_index):
        """
        删除表格中指定索引的行

        Args:
            table: docx.table.Table 对象
            row_index: 要删除的行索引
        """
        tbl = table._tbl
        tr = table.rows[row_index]._tr
        tbl.remove(tr)

    def _remap_numbering_ids_string(self, xml_string, target_num_id="2"):
        """
        重映射编号ID并统一层级为0
        """
        # 1. 替换所有numId
        result = xml_string
        for i in range(20):
            if i != int(target_num_id):
                result = result.replace(f'ns0:numId ns0:val="{i}"', f'ns0:numId ns0:val="{target_num_id}"')

        # 2. 统一所有层级为0（这样所有列表项都是同一级别）
        result = re.sub(r'ns0:ilvl\s+ns0:val="[^"]*"', 'ns0:ilvl ns0:val="0"', result)

        return result


if __name__ == '__main__':
    ex = Extractor()
    tfl_path = '../../data/fuxing/rtf2word.docx'
    tfl = Document(tfl_path)
    # 只提取相关图片
    result = ex.extract_images_between_tables(tfl_path, "图14.2.1.1", "图14.2.2.1")
    for title, img_data in result:
        print(f"提取图片：{title} - 大小 {len(img_data)} bytes")
