from med_write.utils import *
from configurer.yy_nacos import Nacos
from configurer.config_reader import get_med_write_config
import re
import os

from docx.table import Table
from med_write import *


class Summarizer:
    # —— 读取 Prompt 模板 —— #
    def __init__(self):
        # 初始化 clint
        self.client = get_openai_client()

    def generate_summary(self, context):
        prompt_path = "./med_write/prompts/prompt_template1.py"
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            match = re.search(r'prompt_template1\s*=\s*"""(.*?)"""', content, re.DOTALL)

        prompt_template = match.group(1).strip()

        def table_to_text11(table):
            if not table or not isinstance(table, Table):
                return "【未找到表格】"
            text = []
            for row in table.rows:
                row_text = [cell.text.strip() for cell in row.cells]
                text.append(" | ".join(row_text))
            return "\n".join(text)

        prompt = prompt_template.replace("{{受试者分布情况-表名称}}", context.get("受试者分布情况表名称", "【未找到】"))
        prompt = prompt.replace("{{分析数据集-表名称}}", context.get("分析数据集表名称", "【未找到】"))
        prompt = prompt.replace("{{受试者分布情况-表格}}", table_to_text11(context.get("受试者分布情况原始表格")))
        prompt = prompt.replace("{{分析数据集-表格}}", table_to_text11(context.get("分析数据集原始表格")))

        prompt_path = "./med_write/prompts/prompt_template13.py"
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
            match = re.search(r'prompt_template13\s*=\s*"""(.*?)"""', content, re.DOTALL)

        prompt_template2 = match.group(1).strip()
        prompt2 = prompt_template2.replace("{{分析数据集-表名称}}", context.get("分析数据集表名称", "【未找到】"))
        prompt2 = prompt2.replace("{{分析数据集-表格}}",
                                  TableProcessor().table_to_text(context.get("分析数据集原始表格")))
        mse = Nacos()
        config = get_med_write_config()
        MODEL_NAME = config.get("MODEL_NAME")

        try:
            response = self.client.chat.completions.create(
                model=MODEL_NAME,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2,
                max_tokens=1024
            )
            summary = response.choices[0].message.content.strip() if response.choices else "大模型未能生成有效总结，请检查输入内容。"
        except Exception as e:
            summary = f"生成总结时发生错误：{str(e)}"
        try:
            response1 = self.client.chat.completions.create(
                model=MODEL_NAME,
                messages=[{"role": "user", "content": prompt2}],
                temperature=0.2,
                max_tokens=1024
            )
            summary1 = response1.choices[0].message.content.strip() if response1.choices else "大模型未能生成有效总结，请检查输入内容。"
        except Exception as e:
            summary1 = f"生成总结时发生错误：{str(e)}"
        return summary, summary1

    # —— 生成人口统计学资料小结 —— #
    def generate_population_summary(self, title, table):
        path = "./med_write/prompts/prompt_template2.py"
        with open(path, encoding='utf-8') as f:
            content = f.read()
        m = re.search(r'prompt_template2\s*=\s*"""(.*?)"""', content, re.DOTALL)
        prompt = m.group(1).strip()
        prompt = prompt.replace("{{人口统计学资料表名称}}", title)
        prompt = prompt.replace("{{人口统计学资料表格}}", TableProcessor().table_to_text(table))
        mse = Nacos()
        config = get_med_write_config()
        MODEL_NAME = config.get("MODEL_NAME")
        resp = self.client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        return resp.choices[0].message.content.strip()

    def generate_summary_tfl(self, section_name: str, title: str, tables_text: str) -> str:
        if section_name not in PROMPT_CFG:
            raise KeyError(f"未找到对应的 PROMPT 配置：{section_name}")
        prompt_file, title_ph, table_ph = PROMPT_CFG[section_name]
        prompt_path = os.path.join(".", "med_write", "prompts", prompt_file)
        with open(prompt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # 提取 prompt_template 定义的内容
        match = re.search(r'prompt_template\d+\s*=\s*"""(.*?)"""', content, re.DOTALL)
        if not match:
            raise ValueError(f"在 {prompt_file} 中未找到 prompt_template 定义")
        prompt = match.group(1).strip()
        # 替换占位符
        prompt = prompt.replace(title_ph, title)
        prompt = prompt.replace(table_ph, tables_text)
        # 调用大模型
        mse = Nacos()
        config = get_med_write_config()
        MODEL_NAME = config.get("MODEL_NAME")
        resp = self.client.chat.completions.create(
            model=MODEL_NAME,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        return resp.choices[0].message.content.strip()

    def build_summary_content(self, secs):
        """
        把每个节的“表名 / 表格内容 / 注释”拼成纯文本
        """
        lines = []
        for sec in secs:
            lines.append(sec['title'])
            for tbl in sec['tables']:
                for row in tbl.rows:
                    lines.append(" | ".join(cell.text.strip() for cell in row.cells))
            if sec.get('note'):
                lines.append(sec['note'])
            lines.append("")  # 空行分隔
        return "\n".join(lines).strip()
