from docx import Document
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls, qn
import copy
import gc

def convert_breaks_xml_level_v2(input_file, output_file):
    """
    方法2：在XML级别处理软回车转硬回车，完整保留格式
    """
    doc = Document(input_file)

    # 收集需要处理的段落（从后往前处理避免索引问题）
    paragraphs_to_process = []

    for i, paragraph in enumerate(doc.paragraphs):
        p_xml = paragraph._element
        # 查找所有的break元素
        breaks = p_xml.xpath('.//w:br')

        # 检查文本中是否有换行符
        has_text_breaks = False
        for run in paragraph.runs:
            if '\n' in run.text or '\r' in run.text:
                has_text_breaks = True
                break

        if breaks or has_text_breaks:
            paragraphs_to_process.append((i, paragraph))

    # 从后往前处理段落
    for para_index, paragraph in reversed(paragraphs_to_process):
        process_paragraph_with_breaks_xml(paragraph, doc, para_index)

    doc.save(output_file)
    del doc
    gc.collect()

def process_paragraph_with_breaks_xml(paragraph, doc, para_index):
    """在XML级别处理软回车，保留所有格式"""
    p_element = paragraph._element

    # 保存完整的段落属性
    pPr = p_element.find(qn('w:pPr'))
    pPr_copy = None
    if pPr is not None:
        pPr_copy = copy.deepcopy(pPr)

    # 收集所有run和break信息
    content_parts = []

    # 遍历段落中的所有元素
    for child in list(p_element):
        if child.tag == qn('w:r'):  # run元素
            run_content = extract_run_content_with_breaks(child)
            content_parts.extend(run_content)

    # 如果没有软回车，直接返回
    if not any(part['type'] == 'break' for part in content_parts):
        return

    # 分组处理：按break分割内容
    paragraph_groups = []
    current_group = []

    for part in content_parts:
        if part['type'] == 'break':
            if current_group:
                paragraph_groups.append(current_group)
                current_group = []
        else:
            current_group.append(part)

    if current_group:
        paragraph_groups.append(current_group)

    # 如果没有内容组，直接返回
    if not paragraph_groups:
        return

    # 清空原段落内容（保留pPr）
    for child in list(p_element):
        if child.tag != qn('w:pPr'):
            p_element.remove(child)

    # 重建第一个段落
    rebuild_paragraph_content(p_element, paragraph_groups[0])

    # 创建新段落
    current_element = p_element
    for group in paragraph_groups[1:]:
        # 创建新段落
        new_p = create_new_paragraph_with_format(pPr_copy)
        rebuild_paragraph_content(new_p, group)

        # 插入到文档中
        current_element.addnext(new_p)
        current_element = new_p

def extract_run_content_with_breaks(run_element):
    """提取run中的内容和break信息"""
    parts = []

    # 保存run的格式属性
    rPr = run_element.find(qn('w:rPr'))
    rPr_copy = None
    if rPr is not None:
        rPr_copy = copy.deepcopy(rPr)

    for child in run_element:
        if child.tag == qn('w:t'):  # 文本
            text = child.text or ''
            if '\n' in text or '\r' in text:
                # 处理文本中的换行
                text_parts = text.replace('\r\n', '\n').replace('\r', '\n').split('\n')
                for i, part in enumerate(text_parts):
                    if part:
                        parts.append({
                            'type': 'text',
                            'content': part,
                            'rPr': copy.deepcopy(rPr_copy) if rPr_copy is not None else None
                        })
                    if i < len(text_parts) - 1:
                        parts.append({'type': 'break'})
            else:
                if text:  # 只有非空文本才添加
                    parts.append({
                        'type': 'text',
                        'content': text,
                        'rPr': copy.deepcopy(rPr_copy) if rPr_copy is not None else None
                    })
        elif child.tag == qn('w:br'):  # 软回车
            parts.append({'type': 'break'})
        # 处理其他可能的run内容（如tab等）
        elif child.tag == qn('w:tab'):
            parts.append({
                'type': 'tab',
                'rPr': copy.deepcopy(rPr_copy) if rPr_copy is not None else None
            })

    return parts

def rebuild_paragraph_content(p_element, content_parts):
    """重建段落内容"""
    for part in content_parts:
        if part['type'] == 'text' and part['content']:
            # 创建新的run
            run_xml = f'<w:r {nsdecls("w")}></w:r>'
            run_element = parse_xml(run_xml)

            # 添加run属性
            if part.get('rPr') is not None:
                run_element.insert(0, part['rPr'])

            # 添加文本
            text_content = part["content"]
            # 转义XML特殊字符
            text_content = text_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

            t_xml = f'<w:t {nsdecls("w")}>{text_content}</w:t>'
            t_element = parse_xml(t_xml)
            run_element.append(t_element)

            p_element.append(run_element)

        elif part['type'] == 'tab':
            # 创建新的run用于tab
            run_xml = f'<w:r {nsdecls("w")}></w:r>'
            run_element = parse_xml(run_xml)

            # 添加run属性
            if part.get('rPr') is not None:
                run_element.insert(0, part['rPr'])

            # 添加tab
            tab_xml = f'<w:tab {nsdecls("w")}/>'
            tab_element = parse_xml(tab_xml)
            run_element.append(tab_element)

            p_element.append(run_element)

def create_new_paragraph_with_format(pPr_template):
    """创建带格式的新段落"""
    p_xml = f'<w:p {nsdecls("w")}></w:p>'
    new_p = parse_xml(p_xml)

    if pPr_template is not None:
        new_p.insert(0, copy.deepcopy(pPr_template))

    return new_p

# 使用示例
if __name__ == "__main__":
    input_file = "oss-20250527014835-91df9859-c544-4a1c-b662-ae565cafaf2d (1).docxs"
    output_file = "output_v2.docx"

    # 调用方法2
    convert_breaks_xml_level_v2(input_file, output_file)
    print("处理完成！")
