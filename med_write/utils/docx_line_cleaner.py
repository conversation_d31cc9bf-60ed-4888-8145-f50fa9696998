'''
本模块的作用是去除多余的硬回车，只保留最多两个连续的硬回车
'''
from docx import Document
from docx.oxml.text.paragraph import CT_P
from docx.oxml.shared import qn
from docx.oxml.ns import nsdecls
import re
import gc


def has_section_break(paragraph):
    """检查段落是否包含分节符"""
    try:
        p_element = paragraph._element
        # 检查段落属性中是否有分节符
        pPr = p_element.find(qn('w:pPr'))
        if pPr is not None:
            sectPr = pPr.find(qn('w:sectPr'))
            if sectPr is not None:
                return True
        return False
    except:
        return False


def has_page_break(paragraph):
    """检查段落是否包含分页符"""
    try:
        p_element = paragraph._element
        pPr = p_element.find(qn('w:pPr'))
        if pPr is not None:
            pageBreakBefore = pPr.find(qn('w:pageBreakBefore'))
            if pageBreakBefore is not None:
                return True
        return False
    except:
        return False


def save_paragraph_format_info(paragraph):
    """保存段落的完整格式信息"""
    try:
        pf = paragraph.paragraph_format
        format_info = {
            'style': paragraph.style,
            'alignment': pf.alignment,
            'left_indent': pf.left_indent,
            'right_indent': pf.right_indent,
            'first_line_indent': pf.first_line_indent,
            'space_before': pf.space_before,
            'space_after': pf.space_after,
            'line_spacing': pf.line_spacing,
            'line_spacing_rule': pf.line_spacing_rule,
            'keep_together': pf.keep_together,
            'keep_with_next': pf.keep_with_next,
            'page_break_before': pf.page_break_before,
            'widow_control': pf.widow_control,
        }
        return format_info
    except:
        return {}


def apply_paragraph_format_info(paragraph, format_info):
    """应用段落格式信息"""
    try:
        pf = paragraph.paragraph_format

        if format_info.get('style'):
            paragraph.style = format_info['style']
        if format_info.get('alignment') is not None:
            pf.alignment = format_info['alignment']
        if format_info.get('left_indent') is not None:
            pf.left_indent = format_info['left_indent']
        if format_info.get('right_indent') is not None:
            pf.right_indent = format_info['right_indent']
        if format_info.get('first_line_indent') is not None:
            pf.first_line_indent = format_info['first_line_indent']
        if format_info.get('space_before') is not None:
            pf.space_before = format_info['space_before']
        if format_info.get('space_after') is not None:
            pf.space_after = format_info['space_after']
        if format_info.get('line_spacing') is not None:
            pf.line_spacing = format_info['line_spacing']
        if format_info.get('line_spacing_rule') is not None:
            pf.line_spacing_rule = format_info['line_spacing_rule']
        if format_info.get('keep_together') is not None:
            pf.keep_together = format_info['keep_together']
        if format_info.get('keep_with_next') is not None:
            pf.keep_with_next = format_info['keep_with_next']
        if format_info.get('page_break_before') is not None:
            pf.page_break_before = format_info['page_break_before']
        if format_info.get('widow_control') is not None:
            pf.widow_control = format_info['widow_control']
    except Exception as e:
        print(f"应用段落格式时出错: {e}")


def save_run_format_info(run):
    """保存run的格式信息"""
    try:
        return {
            'font_name': run.font.name,
            'font_size': run.font.size,
            'bold': run.font.bold,
            'italic': run.font.italic,
            'underline': run.font.underline,
            'color': run.font.color.rgb if run.font.color.rgb else None,
        }
    except:
        return {}


def apply_run_format_info(run, format_info):
    """应用run的格式信息"""
    try:
        if format_info.get('font_name'):
            run.font.name = format_info['font_name']
        if format_info.get('font_size'):
            run.font.size = format_info['font_size']
        if format_info.get('bold') is not None:
            run.font.bold = format_info['bold']
        if format_info.get('italic') is not None:
            run.font.italic = format_info['italic']
        if format_info.get('underline') is not None:
            run.font.underline = format_info['underline']
        if format_info.get('color'):
            run.font.color.rgb = format_info['color']
    except Exception as e:
        print(f"应用run格式时出错: {e}")


def remove_excessive_line_breaks_preserve_format(input_file, output_file, max_consecutive_breaks=2):
    """
    去除多余的硬回车，同时保持文档格式不变

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        max_consecutive_breaks: 允许的最大连续换行符数量
    """
    doc = Document(input_file)

    # 第一步：处理段落内的软回车
    for paragraph in doc.paragraphs:
        if paragraph.text.strip():  # 只处理非空段落
            original_text = paragraph.text

            # 处理不同类型的换行符
            text = original_text.replace('\r\n', '\n').replace('\r', '\n')

            # 创建正则表达式模式，匹配超过指定数量的连续换行符
            pattern = r'\n{' + str(max_consecutive_breaks + 1) + r',}'
            replacement = '\n' * max_consecutive_breaks

            cleaned_text = re.sub(pattern, replacement, text)

            # 如果文本发生了变化，更新段落但保持格式
            if original_text != cleaned_text:
                # 保存原有格式
                para_format = save_paragraph_format_info(paragraph)
                run_formats = []
                for run in paragraph.runs:
                    run_formats.append({
                        'text': run.text,
                        'format': save_run_format_info(run)
                    })

                # 清空并重建段落
                paragraph.clear()

                # 如果有多个runs，尝试保持原有的run分割
                if len(run_formats) > 1:
                    # 按比例分配新文本到各个runs
                    total_original_length = len(original_text)
                    if total_original_length > 0:
                        for run_info in run_formats:
                            if run_info['text']:
                                # 计算这个run在新文本中应该占的比例
                                ratio = len(run_info['text']) / total_original_length
                                start_pos = int(len(cleaned_text) * sum(len(r['text']) for r in run_formats[
                                                                                                :run_formats.index(
                                                                                                    run_info)]) / total_original_length)
                                end_pos = int(start_pos + len(cleaned_text) * ratio)

                                run_text = cleaned_text[start_pos:end_pos] if start_pos < len(cleaned_text) else ""
                                if run_text:
                                    new_run = paragraph.add_run(run_text)
                                    apply_run_format_info(new_run, run_info['format'])

                    # 如果上述方法没有完全处理文本，添加剩余部分
                    current_text = paragraph.text
                    if current_text != cleaned_text:
                        remaining_text = cleaned_text[len(current_text):]
                        if remaining_text:
                            paragraph.add_run(remaining_text)
                else:
                    # 只有一个run或没有run，直接添加
                    new_run = paragraph.add_run(cleaned_text)
                    if run_formats:
                        apply_run_format_info(new_run, run_formats[0]['format'])

                # 恢复段落格式
                apply_paragraph_format_info(paragraph, para_format)

    # 第二步：处理文档级别的连续空段落
    paragraphs_to_remove = []
    empty_count = 0

    for i, paragraph in enumerate(doc.paragraphs):
        # 检查是否为空段落
        if not paragraph.text.strip():
            # 检查是否包含重要的格式元素（分节符、分页符等）
            if has_section_break(paragraph) or has_page_break(paragraph):
                # 重置计数，不删除包含重要格式的段落
                empty_count = 0
                continue

            empty_count += 1
            if empty_count > max_consecutive_breaks:
                paragraphs_to_remove.append(paragraph)
        else:
            empty_count = 0

    # 安全地删除多余的空段落
    removed_count = 0
    for paragraph in paragraphs_to_remove:
        try:
            p_element = paragraph._element
            parent = p_element.getparent()
            if parent is not None:
                parent.remove(p_element)
                removed_count += 1
        except Exception as e:
            print(f"删除段落时出错: {e}")
            continue

    # 保存文档
    doc.save(output_file)
    del doc
    gc.collect()


# 使用示例
if __name__ == "__main__":
    input_file = "output2.docx"
    output_file = "output_preserve_format1.docx"

    remove_excessive_line_breaks_preserve_format(input_file, output_file, max_consecutive_breaks=2)
