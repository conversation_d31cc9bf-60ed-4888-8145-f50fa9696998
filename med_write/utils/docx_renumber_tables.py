'''
本模块的作用是重新编号文档中的表格和图片
将"表X试验筛选情况-所有筛选受试者"等格式统一为"表 1"、"表 2"等
将"图X 打开闸门时间"等格式统一为"图 1"、"图 2"等
'''
from docx import Document
import re
import gc


def renumber_tables_and_figures(input_file, output_file):
    """
    重新编号文档中的表格和图片

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    doc = Document(input_file)

    # 用于存储找到的表格和图片信息
    table_counter = 1
    figure_counter = 1

    # 定义匹配模式
    table_pattern = r'表\s*[0-9XxＸｘ一二三四五六七八九十]+[^0-9]*'
    figure_pattern = r'图\s*[XxＸｘ一二三四五六七八九十]\s+[^\d\.].*?(?=\s|$|，|。|；|：)'

    # 处理所有段落
    for paragraph in doc.paragraphs:
        if paragraph.text.strip():
            original_text = paragraph.text
            modified_text = original_text

            # 处理表格编号
            table_matches = list(re.finditer(table_pattern, original_text))
            # 从后往前替换，避免位置偏移问题
            for match in reversed(table_matches):
                start, end = match.span()
                # 提取"表"后面的内容描述
                full_match = match.group()

                # 找到第一个非数字、非X、非空格的字符位置
                desc_start = 1  # 跳过"表"字
                while desc_start < len(full_match) and (
                        full_match[desc_start].isspace() or
                        full_match[desc_start].isdigit() or
                        full_match[desc_start].lower() in ['x', 'ｘ'] or
                        full_match[desc_start] in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
                ):
                    desc_start += 1

                # 构建新的表格标题
                if desc_start < len(full_match):
                    description = full_match[desc_start:].strip()
                    if description:
                        new_text = f"表 {table_counter} {description}"
                    else:
                        new_text = f"表 {table_counter}"
                else:
                    new_text = f"表 {table_counter}"

                # 替换文本
                modified_text = modified_text[:start] + new_text + modified_text[end:]
                table_counter += 1

            # 处理图片编号
            figure_matches = list(re.finditer(figure_pattern, modified_text))
            # 从后往前替换，避免位置偏移问题
            for match in reversed(figure_matches):
                start, end = match.span()
                # 提取"图"后面的内容描述
                full_match = match.group()

                # 找到第一个非数字、非X、非空格的字符位置
                desc_start = 1  # 跳过"图"字
                while desc_start < len(full_match) and (
                        full_match[desc_start].isspace() or
                        full_match[desc_start].isdigit() or
                        full_match[desc_start].lower() in ['x', 'ｘ'] or
                        full_match[desc_start] in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
                ):
                    desc_start += 1

                # 构建新的图片标题
                if desc_start < len(full_match):
                    description = full_match[desc_start:].strip()
                    if description:
                        new_text = f"图 {figure_counter} {description}"
                    else:
                        new_text = f"图 {figure_counter}"
                else:
                    new_text = f"图 {figure_counter}"

                # 替换文本
                modified_text = modified_text[:start] + new_text + modified_text[end:]
                figure_counter += 1

            # 如果文本发生了变化，更新段落
            if original_text != modified_text:
                update_paragraph_text_preserve_format(paragraph, original_text, modified_text)

    # 处理表格中的文本
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    if paragraph.text.strip():
                        original_text = paragraph.text
                        modified_text = original_text

                        # 处理表格编号
                        table_matches = list(re.finditer(table_pattern, original_text))
                        for match in reversed(table_matches):
                            start, end = match.span()
                            full_match = match.group()

                            desc_start = 1
                            while desc_start < len(full_match) and (
                                    full_match[desc_start].isspace() or
                                    full_match[desc_start].isdigit() or
                                    full_match[desc_start].lower() in ['x', 'ｘ'] or
                                    full_match[desc_start] in ['一', '二', '三', '四', '五', '六', '七', '八', '九',
                                                               '十']
                            ):
                                desc_start += 1

                            if desc_start < len(full_match):
                                description = full_match[desc_start:].strip()
                                if description:
                                    new_text = f"表 {table_counter} {description}"
                                else:
                                    new_text = f"表 {table_counter}"
                            else:
                                new_text = f"表 {table_counter}"

                            modified_text = modified_text[:start] + new_text + modified_text[end:]
                            table_counter += 1

                        # 处理图片编号
                        figure_matches = list(re.finditer(figure_pattern, modified_text))
                        for match in reversed(figure_matches):
                            start, end = match.span()
                            full_match = match.group()

                            desc_start = 1
                            while desc_start < len(full_match) and (
                                    full_match[desc_start].isspace() or
                                    full_match[desc_start].isdigit() or
                                    full_match[desc_start].lower() in ['x', 'ｘ'] or
                                    full_match[desc_start] in ['一', '二', '三', '四', '五', '六', '七', '八', '九',
                                                               '十']
                            ):
                                desc_start += 1

                            if desc_start < len(full_match):
                                description = full_match[desc_start:].strip()
                                if description:
                                    new_text = f"图 {figure_counter} {description}"
                                else:
                                    new_text = f"图 {figure_counter}"
                            else:
                                new_text = f"图 {figure_counter}"

                            modified_text = modified_text[:start] + new_text + modified_text[end:]
                            figure_counter += 1

                        # 如果文本发生了变化，更新段落
                        if original_text != modified_text:
                            update_paragraph_text_preserve_format(paragraph, original_text, modified_text)

    # 保存文档
    doc.save(output_file)
    del doc
    gc.collect()

    return table_counter - 1, figure_counter - 1  # 返回实际处理的数量


def update_paragraph_text_preserve_format(paragraph, original_text, new_text):
    """
    更新段落文本同时保持格式
    """
    if not paragraph.runs:
        # 如果没有runs，直接添加新文本
        paragraph.add_run(new_text)
        return

    # 保存段落格式
    para_format = save_paragraph_format_for_renumber(paragraph)

    # 如果只有一个run，直接替换
    if len(paragraph.runs) == 1:
        run_format = save_run_format_for_renumber(paragraph.runs[0])
        paragraph.clear()
        new_run = paragraph.add_run(new_text)
        apply_run_format_for_renumber(new_run, run_format)
        apply_paragraph_format_for_renumber(paragraph, para_format)
        return

    # 多个runs的情况，尝试智能替换
    # 计算文本变化的比例
    if len(original_text) > 0:
        # 按比例分配新文本到各个runs
        run_info = []
        for run in paragraph.runs:
            run_info.append({
                'text': run.text,
                'format': save_run_format_for_renumber(run),
                'ratio': len(run.text) / len(original_text) if len(original_text) > 0 else 0
            })

        # 清空段落并重建
        paragraph.clear()

        # 按比例重新分配文本
        current_pos = 0
        for i, info in enumerate(run_info):
            if i == len(run_info) - 1:
                # 最后一个run，取剩余所有文本
                run_text = new_text[current_pos:]
            else:
                # 按比例计算文本长度
                text_length = int(len(new_text) * info['ratio'])
                run_text = new_text[current_pos:current_pos + text_length]
                current_pos += text_length

            if run_text:  # 只有非空文本才创建run
                new_run = paragraph.add_run(run_text)
                apply_run_format_for_renumber(new_run, info['format'])

    # 恢复段落格式
    apply_paragraph_format_for_renumber(paragraph, para_format)


def save_paragraph_format_for_renumber(paragraph):
    """保存段落格式信息"""
    try:
        pf = paragraph.paragraph_format
        return {
            'style': paragraph.style,
            'alignment': pf.alignment,
            'left_indent': pf.left_indent,
            'right_indent': pf.right_indent,
            'first_line_indent': pf.first_line_indent,
            'space_before': pf.space_before,
            'space_after': pf.space_after,
            'line_spacing': pf.line_spacing,
            'line_spacing_rule': pf.line_spacing_rule,
        }
    except:
        return {}


def apply_paragraph_format_for_renumber(paragraph, format_info):
    """应用段落格式信息"""
    try:
        pf = paragraph.paragraph_format
        if format_info.get('style'):
            paragraph.style = format_info['style']
        if format_info.get('alignment') is not None:
            pf.alignment = format_info['alignment']
        if format_info.get('left_indent') is not None:
            pf.left_indent = format_info['left_indent']
        if format_info.get('right_indent') is not None:
            pf.right_indent = format_info['right_indent']
        if format_info.get('first_line_indent') is not None:
            pf.first_line_indent = format_info['first_line_indent']
        if format_info.get('space_before') is not None:
            pf.space_before = format_info['space_before']
        if format_info.get('space_after') is not None:
            pf.space_after = format_info['space_after']
        if format_info.get('line_spacing') is not None:
            pf.line_spacing = format_info['line_spacing']
        if format_info.get('line_spacing_rule') is not None:
            pf.line_spacing_rule = format_info['line_spacing_rule']
    except Exception as e:
        print(f"应用段落格式时出错: {e}")


def save_run_format_for_renumber(run):
    """保存run格式信息"""
    try:
        return {
            'font_name': run.font.name,
            'font_size': run.font.size,
            'bold': run.font.bold,
            'italic': run.font.italic,
            'underline': run.font.underline,
            'color': run.font.color.rgb if run.font.color.rgb else None,
        }
    except:
        return {}


def apply_run_format_for_renumber(run, format_info):
    """应用run格式信息"""
    try:
        if format_info.get('font_name'):
            run.font.name = format_info['font_name']
        if format_info.get('font_size'):
            run.font.size = format_info['font_size']
        if format_info.get('bold') is not None:
            run.font.bold = format_info['bold']
        if format_info.get('italic') is not None:
            run.font.italic = format_info['italic']
        if format_info.get('underline') is not None:
            run.font.underline = format_info['underline']
        if format_info.get('color'):
            run.font.color.rgb = format_info['color']
    except Exception as e:
        print(f"应用run格式时出错: {e}")
