import copy

class TableProcessor:

    def build_subdoc_with_tables(self,tables, tpl):
        '''doc table2markdown'''
        sd = tpl.new_subdoc()
        for tbl in tables:
            sd._body._element.append(copy.deepcopy(tbl._tbl))
        return sd

    # —— 表格转文本 —— #
    def table_to_text(self,table):
        '''大模型总结部分，将 doc 格式改为 markdown 格式'''

        if not table:
            return "【未找到表格】"
        lines = [" | ".join(cell.text.strip() for cell in row.cells) for row in table.rows]
        return "\n".join(lines)

    def tables_to_text(self,tables):
        '''大模型总结部分：多个表格，，将 doc 格式改为 markdown 格式'''

        parts = []
        for idx, tbl in enumerate(tables, start=1):
            header = f'— 表格 Part {idx} —'
            rows = [" | ".join(cell.text.strip() for cell in row.cells) for row in tbl.rows]
            parts.append("\n".join([header] + rows))
        return "\n\n".join(parts)

    def build_sections_block(self,secs):
        '''大模型总结部分：跨章节'''
        blocks = []
        for sec in secs:
            # 1) 表名
            blocks.append(sec['title'])
            # 2) 表格行
            for tbl in sec['tables']:
                for row in tbl.rows:
                    blocks.append(" | ".join(cell.text.strip() for cell in row.cells))
            # 3) 附注（如果有）
            if sec.get('note'):
                blocks.append(sec['note'])
            # 空行分隔
            blocks.append("")
        return "\n".join(blocks).strip()