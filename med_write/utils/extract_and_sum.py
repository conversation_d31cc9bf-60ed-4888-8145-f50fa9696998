from fastapi import FastAP<PERSON>, UploadFile, File, Form, HTTPException
import copy
from docx.oxml import OxmlElement
import httpx
import logging
import time
from docx.oxml.ns import qn
from api.med_api import extract
from docx.shared import Inches
import gc

logger = logging.getLogger(name=__name__)
gc.collect()
from med_write.utils import *
from med_write import *
from med_write.utils.docx_breaks_converter_v2 import convert_breaks_xml_level_v2
from med_write.utils.docx_renumber_tables import renumber_tables_and_figures
import io
from med_write.utils.docx_breaks_converter import convert_breaks_xml_level
from med_write.utils.docx_line_cleaner import remove_excessive_line_breaks_preserve_format
from med_write.prompts.prompt_template_sample_number import prompt_template_sample_number
from configurer.yy_nacos import Nacos
from configurer.config_reader import get_med_write_config
from med_write.services.extractor_service import Extractor
from med_write.services.summarizer_service import Summarizer
from docxtpl import DocxTemplate
from docx import Document
import re
from med_write.utils import main_upload_file
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from docxtpl import DocxTemplate, InlineImage


class ExtractionResult:
    # —— 读取 Prompt 模板 —— #
    def __init__(self):
        self.client = get_openai_client()
        mse = Nacos()
        config = get_med_write_config()
        self.model_name = config.get("MODEL_NAME")
        self.extractor = Extractor()
        self.table_processor = TableProcessor()
        self.summarizer = Summarizer()

    # 按需加载 prompt 模板
    def load_prompt_template(self, idx: int) -> str:
        """按需加载单个 prompt 模板"""
        text = Path(f"./med_write/prompts/prompt_template{idx}.py") \
            .read_text(encoding="utf-8")
        # 预编译好正则并提取主体
        pattern = re.compile(
            rf'prompt_template{idx}\s*=\s*"""(.*?)"""',
            re.DOTALL
        )
        body = pattern.search(text).group(1).strip()
        return body

    # 1. 定义一个统一的调用函数
    def call_api(self, prompt: str):
        return self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2,
            max_tokens=1024
        )

    # 2. 清洗函数，用于那些需要截断到 “。” 前面的场景
    def clean_tfl_summary(self, raw: str) -> str:
        '''
        # 如果secondary_metrics_summary出现中文“表”，就把这个句子去掉不保留
        # 按中文句号分割句子
        # 按句号分割，过滤包含"表"的句子，重新组合
        # 按中文句号分割句子
        '''
        sentences = [s.strip() for s in raw.split('。') if s.strip()]
        # 过滤掉包含表格引用的句子（更精确的过滤条件）
        filtered_sentences = [s for s in sentences if
                              not ('详见表' in s or '见表' in s or s.strip().startswith('表'))]
        # 将过滤后的句子重新组合成字符串
        if filtered_sentences:
            secondary_metrics_summary = '。'.join(filtered_sentences) + '。'
        else:
            secondary_metrics_summary = ''
        return secondary_metrics_summary

    def build_payload(self, secs):
        lines = []
        for sec in secs:
            lines.append(sec['title'])
            for tbl in sec['tables']:
                for row in tbl.rows:
                    lines.append(" | ".join(cell.text.strip() for cell in row.cells))
            if sec.get('note'):
                lines.append(sec['note'])
            lines.append("")
        return "\n".join(lines).strip()

    def extract_write_upload(self, protocol_file, template_file, tfl_path, workflow_run_id):
        # 初始化局部变量
        doc_tfl = None
        protocol_doc = None
        template_doc = None
        # 提取第一页的内容
        meta = self.extractor.extract_protocol_metadata(protocol_file)
        # 装载模板
        # tfl 的模板
        doc_tfl = Document(tfl_path)
        # protocol的模板
        protocol_doc = Document(protocol_file)
        # 模板装载
        template_file = DocxTemplate(template_file)
        # 提取缩略语
        abbr_title, abbr_tables = self.extractor.extract_tables_and_note_abbr(protocol_doc, "缩略词列表")
        # 缩略语表格
        abbr_subdoc = self.table_processor.build_subdoc_with_tables(abbr_tables, template_file)
        # 提取元数据
        report_id = meta.get("report_id")
        clinical_trial_name = meta.get("clinical_trial_name")
        trial_medical_device_name = meta.get("trial_medical_device_name")
        device_model_specification = meta.get("device_model_specification")
        clinical_trial_institution = meta.get("clinical_trial_institution")
        principal_investigator = meta.get("principal_investigator")
        protocol_version_date = meta.get("protocol_version_date")
        sponsor = meta.get("sponsor")

        context_home_page = {
            "方案编号": report_id,
            "临床试验报告试验名称": clinical_trial_name,
            "试验医疗器械名称": trial_medical_device_name,
            "临床试验使用的型号规格": device_model_specification,
            "临床试验机构": clinical_trial_institution,
            "主要研究者": principal_investigator,
            "方案版本号和日期": protocol_version_date,
            "申办单位": sponsor,
        }
        # --- 1. 提取方案摘要数据 ---
        extracted = self.extractor.find_protocol_summary_table_and_extract_data(protocol_file)
        # --- 2. 构建第一部分 context ---
        context = {}
        for rpt_key, proto_key in REPORT_PLACEHOLDERS_MAPPING.items():
            if proto_key is None:
                context[rpt_key] = f"【请在此处填写 {rpt_key}】"
            else:
                context[rpt_key] = extracted.get(proto_key, f"【未找到 “{proto_key}”】")
        # --- 3. 抽取背景 & 目的 ---
        background = self.extractor.extract_research_background(protocol_file) or "【背景未提取】"
        obj = self.extractor.extract_trial_objective(protocol_file) or "【目的未提取】"

        # --- 4. 流程图文字 & 子文档 ---
        flowchart_text = self.extractor.extract_trial_flowchart(protocol_file) or "【流程图文字未提取】"
        flow_table_tbl = self.extractor.extract_trial_flow_table(protocol_file)
        if flow_table_tbl:
            flow_subdoc = template_file.new_subdoc()
            flow_subdoc._body._element.append(copy.deepcopy(flow_table_tbl._tbl))
        else:
            flow_subdoc = None

        # --- 5. 四（二)：受试者选择-提取各章节内容 ---
        # 四（二)：受试者选择-提取各章节内容
        inc = self.extractor.extract_section(protocol_file, "入选标准")
        exc = self.extractor.extract_section(protocol_file, "排除标准")
        ext = self.extractor.extract_section(protocol_file, "退出标准和程序")
        sample_est = self.extractor.extract_section(protocol_file, "样本量估算")
        trial_dev = self.extractor.extract_section(protocol_file, "试验医疗器械")
        control_dev = self.extractor.extract_section(protocol_file, "对照医疗器械")
        efficacy = self.extractor.extract_section(protocol_file, "有效性评价")
        safety = self.extractor.extract_section(protocol_file, "安全性评价")
        allowed_for = self.extractor.extract_section(protocol_file, "适应证")
        black_list = self.extractor.extract_section(protocol_file, "禁忌证")
        warnings = self.extractor.extract_section(protocol_file, "警告及预防措施")
        ethical_considerations = self.extractor.extract_section(protocol_file, "伦理方面的考虑")
        amendment_procedures = self.extractor.extract_section(protocol_file, "试验方案的修订规程")
        informed_consent_documents = self.extractor.extract_section(protocol_file, "知情同意过程和知情同意书文本")
        protocol_deviation = self.extractor.extract_section(protocol_file, "临床试验方案的偏离")
        # 未提取到时的提示
        inc = inc or "【未提取到“入选标准”，请检查文档】"
        exc = exc or "【未提取到“排除标准”，请检查文档】"
        ext = ext or "【未提取到“退出标准和程序”，请检查文档】"
        sample_est = sample_est or "【未提取到“临床试验样本量”，请检查文档】"
        trial_dev = trial_dev or "【未提取到“试验医疗器械”，请检查文档】"
        control_dev = control_dev or "【未提取到“对照医疗器械”，请检查文档】"
        efficacy = efficacy or "【未提取到“有效性评价终点指标及方法”，请检查文档】"
        safety = safety or "【未提取到“安全性终点”，请检查文档】"
        allowed_for = allowed_for or "【未提取到“适应症”，请检查文档】"
        black_list = black_list or "【未提取到“禁忌症”，请检查文档】"
        warnings = warnings or "【未提取到“注意事项”，请检查文档】"
        ethical_considerations = ethical_considerations or "【未提取到“伦理方面的考虑”，请检查文档】"
        amendment_procedures = amendment_procedures or "【未提取到“试验方案修订规程”，请检查文档】"
        informed_consent_documents = informed_consent_documents or "【未提取到“知情同意过程和知情同意书文本”，请检查文档】"
        # 临床试验方案的偏离
        protocol_deviation = protocol_deviation or "【未提取到“临床试验方案偏离”，请检查文档】"
        # --- 6. 第二部分 mappings ---
        context1 = {}
        for placeholder, source in mappings.items():
            content = self.extractor.extract_section(protocol_file, source)
            context1[placeholder] = content or f'【未提取到“{placeholder}”，请检查文档】'

        # --- 六、临床试验结果（一）分析人群 ---
        context3 = {}
        start_idx = 0
        title, table, note, start_idx = self.extractor.extract_table_and_note1(doc_tfl, "受试者分布情况", start_idx)
        context3["受试者分布情况表名称"] = title or "【未找到受试者分布情况标题】"
        context3["受试者分布情况表附注"] = note or "【未找到受试者分布情况附注】"
        context3["受试者分布情况原始表格"] = table  # Store raw table for summary
        if table:
            subdoc = template_file.new_subdoc()
            subdoc._body._element.append(copy.deepcopy(table._tbl))
            context3["受试者分布情况表格"] = subdoc  # Store Subdoc for rendering
        else:
            context3["受试者分布情况表格"] = None
            logger.info(f"⚠ 未检测到受试者分布情况表格")

        # 提取分析数据集
        title, table, note, start_idx = self.extractor.extract_table_and_note1(doc_tfl, "分析数据集", start_idx)
        context3["分析数据集表名称"] = title or "【未找到分析数据集标题】"
        context3["分析数据集表附注"] = note or "【未找到分析数据集附注】"
        context3["分析数据集原始表格"] = table  # Store raw table for summary
        if table:
            subdoc = template_file.new_subdoc()
            subdoc._body._element.append(copy.deepcopy(table._tbl))
            context3["分析数据集表格"] = subdoc  # Store Subdoc for rendering

        else:
            context3["分析数据集表格"] = None
            logger.info(f"⚠ 未检测到受试者分布情况表格")

        # --- 六、临床试验结果（二）人口统计学资料和基线数据 ---
        # 1. 人口统计学资料
        pop_title, pop_table, pop_note = self.extractor.extract_table_and_note(doc_tfl)

        # 添加空值检查
        if pop_table:
            pop_subdoc = template_file.new_subdoc()
            pop_subdoc._body._element.append(copy.deepcopy(pop_table._tbl))
        else:
            pop_subdoc = None
            logger.info(f"⚠ 未检测到人口统计学资料表格")

        # --- 2. 既往/伴随疾病 ---
        comb_title, comb_tables, comb_note = self.extractor.extract_tables_and_note(doc_tfl, "既往/伴随疾病")

        comb_subdoc = self.table_processor.build_subdoc_with_tables(comb_tables, template_file)
        # --- 3. 手术史 ---
        surg_title, surg_tables, surg_note = self.extractor.extract_tables_and_note(doc_tfl, "手术史")

        surg_subdoc = self.table_processor.build_subdoc_with_tables(surg_tables, template_file)
        # --- 4. 过敏史 ---
        allergy_title, allergy_tables, allergy_note = self.extractor.extract_tables_and_note(doc_tfl, "过敏史")

        allergy_subdoc = self.table_processor.build_subdoc_with_tables(allergy_tables, template_file)
        # --- 5. 合并用药 ---
        medication_title, medication_tables, medication_note = self.extractor.extract_tables_and_note(doc_tfl,
                                                                                                      "合并用药")

        medication_subdoc = self.table_processor.build_subdoc_with_tables(medication_tables, template_file)

        # --- 6. 合并非药物治疗 ---
        nonmed_title, nonmed_tables, nonmed_note = self.extractor.extract_tables_and_note(doc_tfl, "合并非药物治疗")

        nonmed_subdoc = self.table_processor.build_subdoc_with_tables(nonmed_tables, template_file)

        # --- 7. 受试眼选择 ---
        eye_title, eye_tables, eye_note = self.extractor.extract_tables_and_note(doc_tfl, "受试眼选择")

        eye_subdoc = self.table_processor.build_subdoc_with_tables(eye_tables, template_file)

        # --- 重要方案偏离情况 ---
        deviation_title, deviation_tables, deviation_note = self.extractor.extract_tables_and_note(doc_tfl,
                                                                                                   "重要方案偏离情况")

        deviation_subdoc = self.table_processor.build_subdoc_with_tables(deviation_tables, template_file)

        # 六（三）主要和次要评价指标
        primary_secs = self.extractor.extract_all_sections(doc_tfl, "表9.2.1")
        secondary_secs = self.extractor.extract_all_sections(doc_tfl, "表9.2.2")
        # 3) 过滤出真正包含表格的节
        primary_valid = [s for s in primary_secs if s['tables']]
        secondary_valid = [s for s in secondary_secs if s['tables']]
        # 5) 构造上下文：填充主要评价指标
        max_slots = 20
        context0 = {}
        # --- 主要评价指标 ---
        for idx, sec in enumerate(primary_valid[:max_slots], start=1):
            # 标题
            context0[f"主要评价指标表名称{idx}"] = sec['title']
            sub = template_file.new_subdoc()
            for tbl in sec['tables']:
                tbl_xml = copy.deepcopy(tbl._tbl)
                sub._body._element.append(tbl_xml)
                sub._body._element.append(OxmlElement('w:p'))
            context0[f"主要评价指标表格{idx}"] = sub
            # 附注
            context0[f"主要评价指标附注{idx}"] = sec.get('note', "")

        # 不足时补空
        for idx in range(len(primary_valid) + 1, max_slots + 1):
            context0[f"主要评价指标表名称{idx}"] = ""
            context0[f"主要评价指标表格{idx}"] = template_file.new_subdoc()
            context0[f"主要评价指标附注{idx}"] = ""

        # --- 次要评价指标 ---
        for idx, sec in enumerate(secondary_valid[:max_slots], start=1):
            context0[f"次要评价指标表名称{idx}"] = sec['title']
            sub = template_file.new_subdoc()
            for tbl in sec['tables']:
                tbl_xml = copy.deepcopy(tbl._tbl)
                sub._body._element.append(tbl_xml)
                sub._body._element.append(OxmlElement('w:p'))
            context0[f"次要评价指标表格{idx}"] = sub
            context0[f"次要评价指标附注{idx}"] = sec.get('note', "")

        for idx in range(len(secondary_valid) + 1, max_slots + 1):
            context0[f"次要评价指标表名称{idx}"] = ""
            context0[f"次要评价指标表格{idx}"] = template_file.new_subdoc()
            context0[f"次要评价指标附注{idx}"] = ""

            # 六（四）安全性评价
            # 1. 不良事件
            all_secs = self.extractor.extract_all_sections(doc_tfl, "不良事件")
            valid_secs_notgood = [s for s in all_secs if s['tables']]

            # 3) 填充 context
            max_slots = 20
            context4 = {}
            for idx, sec in enumerate(valid_secs_notgood[:max_slots], start=1):
                # 原样标题
                context4[f"不良事件表名称{idx}"] = sec['title']
                # 子文档插入表格
                sub = template_file.new_subdoc()
                for tbl_idx, tbl in enumerate(sec['tables'], start=1):
                    tbl_xml = copy.deepcopy(tbl._tbl)
                    sub._body._element.append(tbl_xml)
                    sub._body._element.append(OxmlElement('w:p'))
                context4[f"不良事件表格{idx}"] = sub

                # 附注
                note = sec.get('note', "")
                context4[f"不良事件附注{idx}"] = note

            # 少于 10 个时，用空值填满
            for idx in range(len(valid_secs_notgood) + 1, max_slots + 1):
                context4[f"不良事件表名称{idx}"] = ""
                context4[f"不良事件表格{idx}"] = template_file.new_subdoc()
                context4[f"不良事件附注{idx}"] = ""

            # 六（四）安全性评价
            # 2. 严重不良事件
            context5 = {}
            all_secs = self.extractor.extract_all_sections(doc_tfl, "严重不良事件")
            valid_secs_serious_event = [s for s in all_secs if s['tables']]

            # 3) 填充 context
            max_slots = 20
            for idx, sec in enumerate(valid_secs_serious_event[:max_slots], start=1):
                # 原样标题
                context5[f"严重不良事件表名称{idx}"] = sec['title']

                # 子文档插入表格
                sub = template_file.new_subdoc()
                for tbl_idx, tbl in enumerate(sec['tables'], start=1):
                    tbl_xml = copy.deepcopy(tbl._tbl)
                    sub._body._element.append(tbl_xml)
                    sub._body._element.append(OxmlElement('w:p'))
                context5[f"严重不良事件表格{idx}"] = sub

                # 附注
                note = sec.get('note', "")
                context5[f"严重不良事件附注{idx}"] = note

            # 少于 10 个时，用空值填满
            for idx in range(len(valid_secs_serious_event) + 1, max_slots + 1):
                context5[f"严重不良事件表名称{idx}"] = ""
                context5[f"严重不良事件表格{idx}"] = template_file.new_subdoc()
                context5[f"严重不良事件附注{idx}"] = ""

        # 六（四）安全性评价
        # 3. 器械缺陷
        context6 = {}
        all_secs = self.extractor.extract_all_sections(doc_tfl, "器械缺陷")
        valid_secs_equipment_defect = [s for s in all_secs if s['tables']]
        max_slots = 20
        for idx, sec in enumerate(valid_secs_equipment_defect[:max_slots], start=1):
            # 原样标题
            context6[f"安全性评价器械缺陷表名称{idx}"] = sec['title']
            # 子文档插入表格
            sub = template_file.new_subdoc()
            for tbl in sec['tables']:
                tbl_xml = copy.deepcopy(tbl._tbl)
                # 打印表格的 XML 概要

                sub._body._element.append(tbl_xml)
                sub._body._element.append(OxmlElement('w:p'))
            context6[f"安全性评价器械缺陷表格{idx}"] = sub
            # 附注
            context6[f"安全性评价器械缺陷附注{idx}"] = sec.get('note', "")

        # 少于 10 个时，用空值填满
        for idx in range(len(valid_secs_equipment_defect) + 1, max_slots + 1):
            context6[f"安全性评价器械缺陷表名称{idx}"] = ""
            context6[f"安全性评价器械缺陷表格{idx}"] = template_file.new_subdoc()
            context6[f"安全性评价器械缺陷附注{idx}"] = ""

        #     # 六（五）不良事件及其处理情况 1. 严重不良事件完全总结：根据二级标题进行提取
        ae_text = self.extractor.extract_section_by_heading2(
            protocol_file,
            keyword=SECTION_KEYWORD,
            exclude=EXCLUDE_KEYWORD
        )

        #     # 六（五）不良事件及其处理情况
        #     # 2. 严重不良事件完全总结：根据标题提取，不区分标题
        ae_text1 = self.extractor.extract_section_by_heading(
            protocol_file,
            keyword=SECTION_KEYWORD1,
            exclude=EXCLUDE_KEYWORD1
        )
        # 不良事件处理情况
        all_secs = self.extractor.extract_all_sections(doc_tfl, "不良事件")
        valid_secs_notgood_all = [s for s in all_secs if s['tables']]

        # 人口统计学资料和基线数据总结["人口统计学资料和基线数据总结1"]
        sections = self.extractor.extract_sections_by_prefix(doc_tfl)
        valid_secs_population = [s for s in sections if s['tables']]

        # 大模型并行
        # 主要/次要评价指标
        tmpl18 = self.load_prompt_template(18)
        primary_payload = self.build_payload(primary_valid)
        secondary_payload = self.build_payload(secondary_valid)
        # 填充“主要评价指标”总结
        prompt_main = tmpl18.replace("{{主要评价指标内容}}", primary_payload)
        tmpl19 = self.load_prompt_template(19)
        prompt_sec = tmpl19.replace("{{次要评价指标内容}}", secondary_payload)

        # 不良事件[安全性评价不良事件大模型总结]   cleaned["七严重不良事件大模型总结"]
        tmpl9 = self.load_prompt_template(9)
        prompt_payload = self.table_processor.build_sections_block(valid_secs_notgood[:])
        prompt_ae = tmpl9.replace("{{不良事件内容}}", prompt_payload)

        # 严重不良事件["安全性评价严重不良事件大模型总结"] cleaned["严重不良事件大模型总结"]
        tmpl10 = self.load_prompt_template(10)
        prompt_payload = self.table_processor.build_sections_block(valid_secs_serious_event[:])
        prompt_se = tmpl10.replace("{{严重不良事件内容}}", prompt_payload)

        # 器械缺陷["安全性评价器械缺陷大模型总结"]cleaned["七器械缺陷大模型总结"]
        tmpl11 = self.load_prompt_template(11)
        prompt_payload = self.table_processor.build_sections_block(valid_secs_equipment_defect[:])
        prompt_defect = tmpl11.replace("{{器械缺陷内容}}", prompt_payload)

        # 不良事件处理情况["不良事件处理情况总体说明"]
        tmpl12 = self.load_prompt_template(12)
        prompt_payload = self.table_processor.build_sections_block(valid_secs_notgood_all[:])
        prompt_ae_proc = tmpl12.replace("{{不良事件处理情况总体说明}}", prompt_payload)

        # 人口统计学资料和基线数据总结["人口统计学资料和基线数据总结1"]
        tmpl14 = self.load_prompt_template(14)
        payload = self.summarizer.build_summary_content(valid_secs_population)
        prompt_pop1 = tmpl14.replace("{{人口统计学资料和基线数据总结1}}", payload)

        # 人口统计学资料和基线数据总结["人口统计学资料和基线数据总结2"]
        tmpl15 = self.load_prompt_template(15)
        prompt_payload = self.table_processor.build_sections_block(valid_secs_population[:])
        prompt_pop2 = tmpl15.replace("{{人口统计学资料和基线数据总结2}}", prompt_payload)
        # 生成大模型总结
        # --- 六、临床试验结果（一）分析人群 ---
        # --- 六、临床试验结果（二）人口统计学资料和基线数据 ---
        # 1. 人口统计学资料
        # 1) 构建任务列表：每项是 (结果 key, 调用函数)
        jobs = [
            ("analysis", lambda: self.summarizer.generate_summary(context3)),
            ("population", lambda: self.summarizer.generate_population_summary(pop_title,
                                                                               pop_table) if pop_table else "【未找到人口统计学资料表格】"),
            ("comb", lambda: self.summarizer.generate_summary_tfl(
                "既往/伴随疾病", comb_title,
                self.table_processor.tables_to_text(comb_tables)) if comb_tables else "【未找到既往/伴随疾病表格】"),
            ("surg", lambda: self.summarizer.generate_summary_tfl(
                "手术史", surg_title,
                self.table_processor.tables_to_text(surg_tables)) if surg_tables else "【未找到手术史表格】"),
            ("allergy", lambda: self.summarizer.generate_summary_tfl(
                "过敏史", allergy_title,
                self.table_processor.tables_to_text(allergy_tables)) if allergy_tables else "【未找到过敏史表格】"),
            ("medication", lambda: self.summarizer.generate_summary_tfl(
                "合并用药", medication_title, self.table_processor.tables_to_text(
                    medication_tables)) if medication_tables else "【未找到合并用药表格】"),
            ("nonmed", lambda: self.summarizer.generate_summary_tfl(
                "合并非药物治疗", nonmed_title,
                self.table_processor.tables_to_text(nonmed_tables)) if nonmed_tables else "【未找到合并非药物治疗表格】"),
            ("eye", lambda: self.summarizer.generate_summary_tfl(
                "受试眼选择", eye_title,
                self.table_processor.tables_to_text(eye_tables)) if eye_tables else "【未找到受试眼选择表格】"),
            ("deviation", lambda: self.summarizer.generate_summary_tfl(
                "重要方案偏离情况", deviation_title, self.table_processor.tables_to_text(
                    deviation_tables)) if deviation_tables else "【未找到重要方案偏离情况表格】"),
        ]

        # 2) 提交并行任务
        pool_results = {}
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_name = {
                executor.submit(fn): name
                for name, fn in jobs
            }
            for fut in as_completed(future_to_name):
                name = future_to_name[fut]
                try:
                    pool_results[name] = fut.result()
                except Exception as e:
                    logger.warning(f"任务 {name} 执行失败: {e}")
                    # 根据任务类型提供默认值
                    if name == "analysis":
                        pool_results[name] = ("【分析人群总结生成失败】", "【分析数据集总结生成失败】")
                    else:
                        pool_results[name] = f"【{name} 总结生成失败】"
        gc.collect()
        # 3) 将并行返回的结果写回
        # 分析人群（generate_summary 返回 (summary5, summary6)）
        summary5, summary6 = pool_results["analysis"]
        context3["分析人群大模型总结"] = summary5
        context3["分析数据集总结"] = summary6
        # 拿到“分析人群大模型总结”之后，填充第一页的“样本量报告摘要”

        sample_number_prompt = prompt_template_sample_number.replace("{{受试者人群分析}}", summary5)
        sample_number_resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": sample_number_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        sample_number_resp = sample_number_resp.choices[0].message.content.strip()
        context["样本量报告摘要"] = sample_number_resp

        # 其余直接赋值
        pop_summary = pool_results["population"]
        comb_summary = pool_results["comb"]
        surg_summary = pool_results["surg"]
        allergy_summary = pool_results["allergy"]
        medication_summary = pool_results["medication"]
        nonmed_summary = pool_results["nonmed"]
        eye_summary = pool_results["eye"]
        deviation_summary = pool_results["deviation"]
        # --- 并行化调用 end ---

        # 并行化
        # 3. 把所有 prompt + 其对应的上下文字典 + key 都打平放到一个列表里
        context7 = {}
        context13 = {}
        jobs = [
            (context0, "主要评价指标大模型总结", prompt_main, None),
            (context0, "次要评价指标大模型总结", prompt_sec, None),
            (context4, "安全性评价不良事件大模型总结", prompt_ae, self.clean_tfl_summary),
            (context4, "安全性评价严重不良事件大模型总结", prompt_se, self.clean_tfl_summary),
            (context4, "安全性评价器械缺陷大模型总结", prompt_defect, self.clean_tfl_summary),
            (context7, "不良事件处理情况总体说明", prompt_ae_proc, None),
            (context13, "人口统计学资料和基线数据总结1", prompt_pop1, None),
            (context13, "人口统计学资料和基线数据总结2", prompt_pop2, None),
        ]
        # 4. 并发执行
        with ThreadPoolExecutor(max_workers=8) as exec:
            future_map = {exec.submit(self.call_api, pr): (ctx, key, clf) for (ctx, key, pr, clf) in jobs}
            for fut in as_completed(future_map):
                ctx, key, clf = future_map[fut]
                try:
                    res = fut.result().choices[0].message.content.strip()
                except Exception as e:
                    res = f"【调用失败：{e}】"
                ctx[key] = res
                if clf:
                    ctx[f"七{key}"] = clf(res)

        # 七（三）主要有效性评价指标：主要评价指标大模型总结的部分放在首页的次要评价大模型指标的表格里
        main_metrics_summary = context0["主要评价指标大模型总结"]
        main_metrics_summary = self.clean_tfl_summary(main_metrics_summary)
        context["主要有效性评价指标结果"] = main_metrics_summary

        # 摘要：次要评价指标大模型总结的部分放在首页的次要评价大模型指标的表格里
        secondary_metrics_summary = context0["次要评价指标大模型总结"]
        secondary_metrics_summary = self.clean_tfl_summary(secondary_metrics_summary)
        context["次要评价指标结果报告摘要"] = secondary_metrics_summary

        # 八 临床试验结论：
        resp13 = context13["人口统计学资料和基线数据总结1"]
        new_resp = context4["安全性评价不良事件大模型总结"]
        new_resp1 = context4["安全性评价严重不良事件大模型总结"]
        new_resp2 = context4["安全性评价器械缺陷大模型总结"]
        # 摘要：安全性结果
        # 把后续所有片段放到一个列表里
        parts0 = [
            new_resp,
            new_resp1,
            new_resp2,
        ]
        # 直接拼接
        all_result_safe_result = "\n\n".join(parts0)
        # 模板中包含 {{临床试验结论}} 占位符
        prompt_safe_result = '''你是一个资深的医学写作助手，需要根据背景资料生成临床试验安全性结果：
# 参考示例文案
（1）不良事件
研究期间，纳入分析的112例（100%，112/112）受试者均未发生任何不良事件。
（2）严重不良事件
研究期间，纳入分析的112例（100%，112/112）受试者均未发生任何严重不良事件。
（3）器械缺陷
研究期间，未发生任何器械缺陷。

# 输入
{安全性结果}

生成的段落中，要：
- 各个事件之间用（1）（2）（3）隔开；  
- 生成事件中不要出现表格名称和表格编号。

请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''

        # 使用 format 方法填充 all_result 到模板中
        final_prompt = prompt_safe_result.format(安全性结果=all_result_safe_result)
        resp15 = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp15 = resp15.choices[0].message.content.strip()
        context0["安全性结果"] = resp15

        all_result = str(context)
        # 原先 all_result 已经是 str(context) 或其它初始内容
        initial = all_result
        # 把后续所有片段放到一个列表里
        parts = [
            initial,
            summary5,
            resp13,
            comb_summary,
            surg_summary,
            allergy_summary,
            medication_summary,
            nonmed_summary,
            eye_summary,
            new_resp,
            new_resp1,
            new_resp2,
            deviation_summary,
        ]
        # 直接拼接
        all_result = "\n\n".join(parts)
        # 模板中包含 {{临床试验结论}} 占位符
        prompt = '''你是一个资深的医学写作助手，需要根据背景资料生成临床试验结论：

                        # 参考示例文案
                        “执鼎医疗科技（杭州）有限公司生产的光相干断层扫描仪（型号：Luminor-A80）用于眼后节血管成像检查的前瞻性、单中心、随机、自身对照、一致性评价的临床试验中，纳入受试者的试验组与对照组影像特征分析结果的一致率达到预先设定的目标值，表明采用试验医疗器械进行眼后节血管成像检查具有临床有效性；同时研究者对使用研究器械时的各项性能评价结果均为满意，器械操作简单、使用方便、软件运行良好；研究期间，无任何不良事件、严重不良事件和器械缺陷发生，表明试验医疗器械临床使用的安全性较好。结论：试验医疗器械用于眼后节血管成像检查具有良好的安全有效性，可以应用于临床。”
                        # 输入
                        {临床试验结论}

                        生成的段落中，要：
                        - 保留示例文案的行文结构和标点格式；语气正式；  
                        - 生成事件中不要出现表格名称和表格编号。
                        - 需要是概括性的结论，不能出现数字。

                        请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''

        # 使用 format 方法填充 all_result 到模板中
        final_prompt = prompt.format(临床试验结论=all_result)
        resp15 = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp15 = resp15.choices[0].message.content.strip()
        context7["临床试验结论"] = resp15

        # 生成目录
        subdoc_toc = template_file.new_subdoc()
        # 在子文档中添加一个段落
        # 在子文档中添加一个段落
        paragraph = subdoc_toc.add_paragraph()

        # 创建 TOC 字段的开始标记
        fldChar_begin = OxmlElement('w:fldChar')
        fldChar_begin.set(qn('w:fldCharType'), 'begin')

        # 创建 TOC 字段的指令文本
        instrText = OxmlElement('w:instrText')
        instrText.set(qn('xml:space'), 'preserve')
        instrText.text = 'TOC \\o "1-3" \\h \\z \\u'

        # 创建 TOC 字段的分隔标记
        fldChar_separate = OxmlElement('w:fldChar')
        fldChar_separate.set(qn('w:fldCharType'), 'separate')

        # 创建 TOC 字段的结束标记
        fldChar_end = OxmlElement('w:fldChar')
        fldChar_end.set(qn('w:fldCharType'), 'end')

        # 将上述元素添加到段落中
        r = paragraph.add_run()
        r._r.append(fldChar_begin)
        r._r.append(instrText)
        r._r.append(fldChar_separate)
        r._r.append(fldChar_end)

        # 构建返回结构
        full_context = {
            **context_home_page,  # 首页
            # 缩略语
            "缩略语表": abbr_subdoc,
            "目录": subdoc_toc,
            **context,  # 第一部分所有字段

            # 背景 & 目的
            "临床试验的背景": background,
            "临床试验目的": obj,

            # 流程图
            "试验流程图文字": flowchart_text,
            **({"试验流程图表格": flow_subdoc} if flow_subdoc else {}),

            # 受试者选择
            "入选标准": inc,
            "排除标准": exc,
            "退出标准和程序": ext,
            "临床试验样本量": sample_est,
            "试验医疗器械": trial_dev,
            "对照医疗器械": control_dev,
            "有效性评价终点指标及方法": efficacy,
            "安全性终点": safety,
            # 插入适应症，禁忌症，注意事项
            "适应症": allowed_for,
            "禁忌症": black_list,
            "注意事项": warnings,

            ## 六、临床试验结果（三）有效性评价
            **context0,
            # 第二部分 mappings
            **context1,
            # 六、临床试验结果（一）分析人群
            **context3,

            # 人口统计学资料 - 添加空值检查
            "人口统计学资料大模型总结": pop_summary or "【人口统计学资料总结未生成】",
            "人口统计学资料表名称": pop_title or "【未找到人口统计学资料标题】",
            **({"人口统计学资料表格": pop_subdoc} if pop_subdoc else {}),
            "人口统计学资料附注": pop_note or "【未找到人口统计学资料附注】",

            # 既往伴随疾病
            "既往伴随疾病大模型总结": comb_summary or "【既往伴随疾病总结未生成】",
            "既往伴随疾病表名称": comb_title or "【未找到既往伴随疾病标题】",
            "既往伴随疾病表格": comb_subdoc,
            "既往伴随疾病附注": comb_note or "【未找到既往伴随疾病附注】",

            # 手术史
            "手术史大模型总结": surg_summary or "【手术史总结未生成】",
            "手术史表名称": surg_title or "【未找到手术史标题】",
            "手术史表格": surg_subdoc,
            "手术史附注": surg_note or "【未找到手术史附注】",

            # 过敏史
            "过敏史大模型总结": allergy_summary or "【过敏史总结未生成】",
            "过敏史表名称": allergy_title or "【未找到过敏史标题】",
            "过敏史表格": allergy_subdoc,
            "过敏史附注": allergy_note or "【未找到过敏史附注】",

            # 合并用药
            "合并用药大模型总结": medication_summary or "【合并用药总结未生成】",
            "合并用药表名称": medication_title or "【未找到合并用药标题】",
            "合并用药表格": medication_subdoc,
            "合并用药附注": medication_note or "【未找到合并用药附注】",

            # 合并非药物治疗
            "合并非药物治疗大模型总结": nonmed_summary or "【合并非药物治疗总结未生成】",
            "合并非药物治疗表名称": nonmed_title or "【未找到合并非药物治疗标题】",
            "合并非药物治疗表格": nonmed_subdoc,
            "合并非药物治疗附注": nonmed_note or "【未找到合并非药物治疗附注】",

            # 受试眼选择
            "受试眼选择大模型总结": eye_summary or "【受试眼选择总结未生成】",
            "受试眼选择表名称": eye_title or "【未找到受试眼选择标题】",
            "受试眼选择表格": eye_subdoc,
            "受试眼选择附注": eye_note or "【未找到受试眼选择附注】",

            # 不良事件
            **context4,
            **context5,
            **context6,
            # 总体说明
            "不良事件总体说明": ae_text or "【不良事件总体说明未提取】",
            "严重不良事件总体说明": ae_text1 or "【严重不良事件总体说明未提取】",
            # 不良事件处理情况总体说明
            **context7,
            **context13,
            "临床试验结论": resp15,
            # 伦理方面的考虑，试验方案的修订规程，知情同意过程和知情同意书文本
            "伦理方面的考虑": ethical_considerations,
            "试验方案的审批": amendment_procedures,
            "知情同意过程和知情同意书": informed_consent_documents,
            # 临床试验方案的偏离
            "临床试验方案的偏离直接复制": protocol_deviation,
            "临床试验方案的偏离总结": deviation_summary or "【临床试验方案的偏离总结未生成】",
            "临床试验方案的偏离表名称1": deviation_title or "【未找到临床试验方案的偏离标题】",
            "临床试验方案的偏离表格1": deviation_subdoc,
            "临床试验方案的偏离附注1": deviation_note or "【未找到临床试验方案的偏离附注】",
        }

        try:
            template_file.render(full_context)
            gc.collect()
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"渲染模板失败: {e}")
        from pathlib import Path
        # 3. 构建输出路径，使用 workflow_run_id 命名
        output_dir = Path("../data/temp")
        output_dir.mkdir(parents=True, exist_ok=True)
        # 如果你想保持原始名字，也可以使用 f"{workflow_run_id}.docx"
        out_path = output_dir / f"{workflow_run_id}.docx"

        template_file.save(str(out_path))
        gc.collect()

        try:
            # 记录用时
            start_time = time.time()  # 记录开始时间
            # 处理软回车，全部转为硬回车
            processed_path = output_dir / f"{workflow_run_id}_processed.docx"
            convert_breaks_xml_level(str(out_path), str(processed_path))

            # 确保文件操作完成
            time.sleep(0.1)  # 短暂等待

            # 删除原始文件，将处理后的文件重命名为原始文件名
            if out_path.exists():
                out_path.unlink()
            if processed_path.exists():
                processed_path.rename(out_path)

            # 再次确保文件系统操作完成
            time.sleep(0.1)
            gc.collect()

            logger.info(f"✔️ 软回车转硬回车处理完成")
            end_time = time.time()  # 记录结束时间
            execution_time = end_time - start_time  # 计算总耗时
            logger.info(f"总执行时间: {execution_time:.4f}秒")
        except Exception as e:
            logger.warning(f"⚠️ 软回车转硬回车处理失败: {e}")

        try:
            # 确保文件存在且可访问
            if not out_path.exists():
                raise FileNotFoundError(f"文件不存在: {out_path}")

            # 去除多余的硬回车换行符（两个以下）
            line_breaks_processed_path = output_dir / f"{workflow_run_id}_line_breaks_processed.docx"

            remove_excessive_line_breaks_preserve_format(str(out_path), str(line_breaks_processed_path),
                                                         max_consecutive_breaks=2)

            # 确保处理完成
            time.sleep(0.1)

            # 文件替换操作
            if out_path.exists():
                out_path.unlink()
            if line_breaks_processed_path.exists():
                line_breaks_processed_path.rename(out_path)

            logger.info(f"✔️ 多余换行符去除处理完成")
            end_time = time.time()  # 记录结束时间
            execution_time = end_time - start_time  # 计算总耗时
            logger.info(f"硬回车多余去除总执行时间: {execution_time:.4f}秒")
        except Exception as e:
            logger.warning(f"⚠️ 多余换行符去除处理失败: {e}")
            logger.warning(f"详细错误信息: {str(e)}")

        # 最终确保文件存在
        if not out_path.exists():
            logger.error(f"❌ 最终文件不存在: {out_path}")
            raise FileNotFoundError(f"处理后的文件丢失: {out_path}")

        # 返回 oss 下载链接
        oss_response_data = main_upload_file(workflow_run_id, str(out_path))
        logger.info(f"✔️ 文件保存成功，正在上传到 OSS，请稍等...")

        # 删除本地临时文件
        try:
            out_path.unlink()  # 使用 pathlib 的 unlink 方法删除文件
            logger.info(f"🗑️ 已删除临时文件: '{out_path}'")

        except Exception as e:
            logger.info(f"⚠️ 删除临时文件失败: '{out_path}'")
        gc.collect()
        return oss_response_data

    def extract_write_upload_fosun(self, protocol_file, template_file, tfl_path, workflow_run_id):
        # 初始化局部变量
        doc_tfl = None
        protocol_doc = None
        template_doc = None

        # 装载模板
        # tfl 的模板
        doc_tfl = Document(tfl_path)
        # protocol的模板
        protocol_doc = Document(protocol_file)
        # 模板装载
        template_file = DocxTemplate(template_file)

        # 1. 标题页
        meta = self.extractor.extract_protocol_metadata_fosun(protocol_file)
        # 提取元数据
        # 申办方
        sponsor = meta.get("sponsor")
        # 申办方联系人
        sponsor_contact = meta.get("sponsor_contact")
        # 申办方联系方式
        sponsor_contact_number = meta.get("sponsor_contact_number")
        context_home_page = {
            "申办方申请人": sponsor,
            "申办方工作单位": sponsor_contact,
            "申办方联系方式": sponsor_contact_number
        }
        # --- 2.摘要 ---
        extracted = self.extractor.find_protocol_summary_table_and_extract_data(protocol_file)
        context = {}
        for rpt_key, proto_key in REPORT_PLACEHOLDERS_MAPPING_FOSUN.items():
            if proto_key is None:
                context[rpt_key] = f"【请在此处填写 {rpt_key}】"
            elif rpt_key == "活性成分名称摘要":
                stats = extracted.get("研究药物", "")
                # 去掉stats 里边的中文字符
                cleaned_stats = re.sub(r'[\u4e00-\u9fa5]', '', stats)
                context[rpt_key] = cleaned_stats if cleaned_stats else "【活性成分名称-摘要未提取，请检查】"
            else:
                context[rpt_key] = extracted.get(proto_key, f"【未找到 “{proto_key}”】")
        # 摘要中的研究者
        # 使用protocol_doc = Document(protocol_file)读取文档，然后读取每一个表格，看表格第一行第一列是否包含字符串"研究者姓名"
        # 如果包含，我们就拿到了我们所需要的表格。然后读取表格，拿到第一行第一列的字符串。
        researcher_name = ""
        for table_index, table in enumerate(protocol_doc.tables):
            # 检查表格是否有行和列
            if len(table.rows) > 0 and len(table.rows[0].cells) > 0:
                # 获取第一行第一列的内容
                first_cell = table.rows[0].cells[0]
                cell_text = first_cell.text.strip()
                # 检查是否包含"研究者姓名"
                if "研究者姓名" in cell_text:
                    # 去掉cell_text里边的“主要研究者姓名：”字段
                    researcher_name = cell_text.replace("主要研究者姓名：", "")
            else:
                logger.info(f"第 {table_index + 1} 个表格为空")

        # 抽取方案批准签字页：申办方名称
        extracted = self.extractor.find_protocol_summary_table_and_extract_data(protocol_file, "方案批准签字页")
        context_approval = {
            "申办方公司名称摘要": extracted.get("申办方名称", "【未找到 “申办方名称”】")
        }
        for rpt_key, proto_key in context_approval.items():
            context[rpt_key] = proto_key

        # 4 缩略语表
        abbr_title, abbr_tables = self.extractor.extract_tables_and_note_abbr(protocol_doc, "缩略语")
        # 缩略语表格
        abbr_subdoc = self.table_processor.build_subdoc_with_tables(abbr_tables, template_file)
        # 研究背景：提取本heading 和下一个 heading 直接的正文内容
        research_background = self.extractor.extract_section_fusun(protocol_file, "医学背景")
        # 研究药物：提取本heading 和下一个 heading 直接的正文内容
        research_medicine = self.extractor.extract_section_fusun(protocol_file, "研究药物介绍")
        # 研究目的：
        research_purpose = self.extractor.extract_section_raw_xml(protocol_file, "研究目的")
        context["研究目的"] = research_purpose

        # 研究终点：
        research_end = self.extractor.extract_section_raw_xml(protocol_file, "研究终点")
        context["研究终点"] = research_end

        # ---------------------------------------------------------
        # 开始 tfl 部分：
        # 10.研究患者 10.1患者分布：参考【TFL】文档的表14.1.1.1
        patient_overall_title, patient_overall_tables, patient_overall_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.1.1.1")
        if patient_overall_tables:
            context["患者分布表名称"] = patient_overall_title
            subdoc = template_file.new_subdoc()
            for tbl in patient_overall_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["患者分布表格"] = subdoc

        # 大模型总结部分
        patient_overall_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“该研究I期阶段共筛选39例患者，其中25例（64.1%）筛选成功，14例（35.9%）筛选失败。在筛选失败的患者中，最常见的筛选失败原因为不符合入选标准或符合排除标准（10例[25.6%]）”

# 真实输入
{患者分布}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = patient_overall_template.format(患者分布=patient_overall_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["患者分布大模型总结"] = resp
        context["患者分布摘要"] = resp

        # 10.研究患者 10.1 患者分布
        patient_overall_title, patient_overall_tables, patient_overall_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "********")
        if patient_overall_tables:
            context["患者分布详情表名称"] = patient_overall_title
            subdoc = template_file.new_subdoc()
            for tbl in patient_overall_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["患者分布详情表格"] = subdoc
            # 大模型总结部分
        patient_overall_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“25例患者分别分配至7个爬坡剂量组，分别为50 mg（1例），100 mg（1例），200 mg（4例），400 mg（4例），600 mg（7例），800 mg（5例）和1000 mg（3例）。至数据提取日，所有患者均已结束治疗，结束治疗的最主要原因为疾病进展（15例[60.0%]），其次为申办方通知结束临床研究（4例[16.0%]），仅1例（4.0%）患者因与研究治疗相关的不可耐受的毒性反应结束治疗。所有患者均已结束试验，结束试验的原因包括该研究已结束（16例[64.0%]）和死亡（9例[36.0%]。”

# 真实输入
{患者分布}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = patient_overall_template.format(患者分布=patient_overall_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["患者分布详情大模型总结"] = resp
        context["患者分布详情摘要"] = resp

        # 10.2 方案偏离
        analyze_data_title, analyze_data_tables, analyze_data_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.1.4")
        if analyze_data_tables:
            context["方案偏离表名称"] = analyze_data_title
            subdoc = template_file.new_subdoc()
            for tbl in analyze_data_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["方案偏离表格"] = subdoc
        # 大模型总结部分
        analyze_data_template = '''你是一个资深的医学csr写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入生成一段专业的中文描述：
# 参考示例文案
“4例（16.0%）患者在研究过程中报告重大方案偏离，其中2例（8.0%）患者报告的重大方案偏离与研究评估相关，2例（8.0%）与研究程序相关，1例（4.0%）与伴随用药相关。”

# 真实输入
{analyze_data}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = analyze_data_template.format(analyze_data=analyze_data_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["方案偏离大模型总结"] = resp
        context["方案偏离摘要"] = resp

        # 11.1 分析数据集
        analyze_data_title, analyze_data_tables, analyze_data_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.1.2")
        if analyze_data_tables:
            context["分析数据集表名称"] = analyze_data_title
            subdoc = template_file.new_subdoc()
            for tbl in analyze_data_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["分析数据集表格"] = subdoc
        # 大模型总结部分
        analyze_data_template = '''你是一个资深的医学csr写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入生成一段专业的中文描述：
# 参考示例文案
“所有入组的25例（100%）患者均接受过至少一次FCN-338给药，且有用药后安全性评价，纳入FAS集和SS集；16例（64.0%）患者纳入DLT分析集（DLTS）；所有25例（100%）患者均纳入PKCS和PKPS分析集。”

# 真实输入
{analyze_data}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = analyze_data_template.format(analyze_data=analyze_data_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["分析数据集大模型总结"] = resp
        context["分析数据集摘要"] = resp

        # 11.2.1 人口统计学
        population_statistics_title, population_statistics_tables, population_statistics_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.1.3")
        if population_statistics_tables:
            context["人口统计学表名称"] = population_statistics_title
            subdoc = template_file.new_subdoc()
            for tbl in population_statistics_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["人口统计学表格"] = subdoc
        # 大模型总结部分
        population_statistics_template = '''你是一个资深的医学csr写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入生成一段专业的中文描述：
# 参考示例文案
“基于FAS集进行人口统计学和基线特征分析，25例患者的中位年龄为60.0岁（范围：34-72岁），大部分（17例[68.0%]）患者年龄＜65岁。男女患者比例相当，其中男性12例（48.0%），女性13例（52.0%）。”

# 真实输入
{population_statistics}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = population_statistics_template.format(population_statistics=population_statistics_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["人口统计学大模型总结"] = resp
        context["人口统计学和其他基线特征摘要"] = resp

        ## 11.2.2 肿瘤病史
        tumor_history_title, tumor_history_tables, tumor_history_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.1.5")
        if tumor_history_tables:
            context["肿瘤病史表名称"] = tumor_history_title
            subdoc = template_file.new_subdoc()
            for tbl in tumor_history_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["肿瘤病史表格"] = subdoc
        # 大模型总结部分
        tumor_history_template = '''你是一个资深的医学csr写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入生成一段专业的中文描述：

# 参考示例文案
“纳入FAS集的25例患者自首次确诊至入组平均时间为42.367个月（±29.6380）（范围：8.48-97.41个月）。按疾病类型分类和相应的预后评分风险分组，13例（52.0%）患者为滤泡性淋巴瘤，其中低危、中危和高危分别为4例、4例和5例；6例（24.0%）患者为弥漫大B细胞淋巴瘤，其中3例为低-中危，低危、高-中危和高危各1例；3例（12.0%）患者为边缘区淋巴瘤；2（8.0%）例患者为套细胞淋巴瘤，低危、中危各1例；剩余1例（4.0%）患者为高危淋巴浆细胞淋巴瘤/华氏巨球蛋白血症。大多数患者（19例[76.0%]）基线ECOG评分为1，其他6例（24.0%）患者基线ECOG评分为0。在25例患者中，6例（24.0%）有骨髓侵犯；有23例（92.0%）患者进行了肿瘤负荷评估，所有淋巴结直径均＜10 cm。按Lugano 2014分期划分，超过一半（18例[72.0%]）患者为III期或IV期。”

# 真实输入
{tumor_history}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = tumor_history_template.format(tumor_history=tumor_history_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["肿瘤病史大模型总结"] = resp

        # 11.4表格部分
        # 参考【TFL】文档的表********
        best_effect_title, best_effect_tables, best_effect_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "表********")
        if best_effect_tables:
            context["最佳总体疗效表名称"] = best_effect_title
            subdoc = template_file.new_subdoc()
            for tbl in best_effect_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["最佳总体疗效表格"] = subdoc

        # 大模型总结部分
        best_effect_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“基于FAS集，对不同剂量组治疗后的最佳总体疗效进行分析。在25例患者中，有1例（4.0%）患者的最佳总体疗效为CR，在600 mg剂量组；有4例（16.0%）患者最佳总体疗效为PR，100 mg组、200 mg组、400 mg组和600 mg组各1例。所有剂量下总体ORR为20.0%（95% CI：6.8%，40.7%）。在100 mg组、200 mg组、400 mg组和600 mg组均观察到客观缓解，ORR的点估计值分别为100%（1/1），25.0%（1/4），25.0%（1/4）和28.6%（2/7）。\n6例（24.0%）患者的最佳总体疗效为无缓解或SD，200 mg、400 mg组和800 mg组各2例，总体DCR为44.0%（95% CI：24.4%，65.1%）。100 mg组、200 mg组、400 mg组、600 mg组和800 mg组的DCR点估计值分别为100%（1/1），75.0%（3/4），75.0%（3/4），28.6%（2/7）和40.0%（2/5）。\n在达到客观缓解的5例患者中，至肿瘤缓解时间中位数为2.07个月（范围：2.0-3.8个月）。”

# 真实输入
{最佳疗效}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = best_effect_template.format(最佳疗效=best_effect_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["最佳总体疗效大模型总结"] = resp
        context["疗效结果摘要"] = resp

        # 11.4图片部分
        extracted_images = self.extractor.extract_images_between_tables(
            tfl_path,
            "图********",
            "图********"
        )

        # Iterate over the extracted images and add them to the context
        for idx, (modified_title, original_id, image_binary) in enumerate(extracted_images, start=1):
            # Create an InlineImage object from the binary data
            image_stream = io.BytesIO(image_binary)
            inline_image = InlineImage(template_file, image_stream, width=Inches(6))

            # Add the modified title, original id and image to the context with unique keys
            context[f'最佳总体疗效图片标题{idx}'] = modified_title
            context[f'最佳总体疗效图片{idx}'] = inline_image
            context[f'最佳总体疗效数据来源{idx}'] = original_id

        # 12.1 暴露程度drug_exposure_situation
        drug_exposure_situation_title, drug_exposure_situation_tables, drug_exposure_situation_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "14.3.1.2")
        if drug_exposure_situation_tables:
            context["暴露程度表名称"] = drug_exposure_situation_title
            subdoc = template_file.new_subdoc()
            for tbl in drug_exposure_situation_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["暴露程度表格"] = subdoc
        # 大模型总结部分
        drug_exposure_situation_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“基于SS集，对整个治疗期间研究药物暴露情况进行分析。纳入SS集的25例患者，均根据固定剂量爬坡计划给药，在PK导入期按分配的剂量组接受一次特定剂量的研究药物给药后进入连续给药期。在连续给药期，按分配的剂量组，患者每日接受一次口服FCN-338给药，28天为一个周期，总暴露持续时间中位数为58.0天（范围：2-833天）。医嘱给药强度中位数为600.00 mg/天（范围：50.0-1000.0 mg/天），实际给药强度中位数为581.25 mg/天（范围：50.0-1000.0 mg/天），相对用药强度中位数为100.00%（范围：55.2-100.0%）。有22例（88.0%）患者相对用药强度在80%至120%，其他8例（12.0%）患者相对用药强度＜80%。”

# 真实输入
{暴露程度}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 针对表格中除了“依从性”之外的结果进行描述
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = drug_exposure_situation_template.format(暴露程度=drug_exposure_situation_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["暴露程度大模型总结"] = resp
        # 11.3 依从性
        drug_exposure_situation_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“基于SS集，对研究药物的用药依从性进行分析，25例患者用药依从性中位数为100.00%（范围：96.9-120.0%），所有患者的用药依从性均在80%至120%之间。除200 mg、400 mg和600 mg外，其他剂量组患者用药依从性均为100.0%，整体看来，研究过程中患者用药依从性良好。”

# 真实输入
{暴露程度}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 表格上部分不需要总结，只对“依从性”部分进行总结
- 列出表中“依从性”部分的核心数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = drug_exposure_situation_template.format(暴露程度=drug_exposure_situation_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["治疗依从性大模型总结"] = resp

        # 12.2 安全性结果DTL
        DTL_title, DTL_tables, DTL_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "********")
        if DTL_tables:
            context["DLT表名称"] = DTL_title
            subdoc = template_file.new_subdoc()
            for tbl in DTL_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["DLT表格"] = subdoc
        # 大模型总结部分
        DTL_template = '''你是一个资深的医学csr写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入生成一段专业的中文描述：
# 参考示例文案
“在剂量递增试验中，共纳入14例患者（2 mg组N=1，5 mg组N=1，10 mg组N=3，20 mg组N=3，30 mg组N=3，40 mg组N=3）。40 mg组（N=3）报告1例DLT（血小板计数降低），其余剂量组无DLT。”

# 真实输入
{DTL}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = DTL_template.format(DTL=DTL_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["DLT大模型总结"] = resp

        # 12.3 不良事件adverse_drug_events
        adverse_drug_events_title, adverse_drug_events_tables, adverse_drug_events_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "********")
        if adverse_drug_events_tables:
            context["不良事件表名称"] = adverse_drug_events_title
            subdoc = template_file.new_subdoc()
            for tbl in adverse_drug_events_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["不良事件表格"] = subdoc
        # 大模型总结部分
        adverse_drug_events_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“各剂量组按系统器官分类（SOC）和首选术语（PT）呈现的，发生率≥10%的TEAE总结见下表。\n报告的发生率排名前三的SOC为各类检查（23例[92.0%]），代谢及营养类疾病（15例[60.0%]）和血液及淋巴系统疾病（12例[48.0%]）。\n按PT划分，发生率≥20%的TEAE按发生率从高到低排序为：白细胞计数降低（16例[64.0%]），中性粒细胞计数降低（13例[52.0%]），贫血（12例[48.0%]），淋巴细胞计数降低（11例[44.0%]），低白蛋白血症（10例[40.0%]），血小板计数降低（9例[36.0%]），低钾血症（9例[36.0%]），低钙血症（8例[32.0%]），高尿酸血症（8例[32.0%]），β2微球蛋白升高（7例[28.0%]），球蛋白降低（6例[24.0%]），高甘油三酯血症（6例[24.0%]），高血糖症（6例[24.0%]），尿路感染（6例[24.0%]），丙氨酸氨基转移酶升高（5例[20.0%]），心电图QRS波群延长（5例[20.0%]），心电图ST-T段变化（5例[20.0%]），红细胞沉降率升高（5例[20.0%]），血乳酸脱氢酶升高（5例[20.0%]），血胆红素升高（5例[20.0%]），高密度脂蛋白降低（5例[20.0%]），高磷酸血症（5例[20.0%]）。”

# 真实输入
{不良事件}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = adverse_drug_events_template.format(不良事件=adverse_drug_events_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["不良事件大模型总结"] = resp

        # ******** 不良事件TEAE
        TEAE_title, TEAE_tables, TEAE_markdown = self.extractor.extract_tables_by_cell_content(
            doc_tfl, "********")
        if TEAE_tables:
            context["TEAE表名称"] = TEAE_title
            subdoc = template_file.new_subdoc()
            for tbl in TEAE_tables:
                subdoc._body._element.append(copy.deepcopy(tbl._tbl))
                # subdoc._body._element.append(OxmlElement('w:p'))  # 可选：添加空段落作为分隔
            context["TEAE表格"] = subdoc
        # 大模型总结部分
        TEAE_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“在SS集中，在≥2例患者中报告的CTCAE≥3级的TEAE包括中性粒细胞计数降低（5例[20.0%]），淋巴细胞计数降低（4例[16.0%]），白细胞计数降低（3例[12.0%]）和贫血（2例[8.0%]），其他CTCAE≥3级的TEAE均在个例患者中报告。”

# 真实输入
{TAE}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 生成事件中不要出现表格名称和表格编号
- 列出表中核心的数据
请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = TEAE_template.format(TAE=TEAE_markdown)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["TEAE大模型总结"] = resp

        # 12.7 安全性结论
        safety_conclusion="临床试验名称:"+context["研究标题摘要"]+"研究药物名称:"+context["产品名称摘要"]+context["暴露程度大模型总结"]+"DTL总结部分："+context["DLT大模型总结"]+context["不良事件大模型总结"]+context["TEAE大模型总结"]
        # 大模型总结部分
        safety_conclusion_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

        # 参考示例文案
        “在纳入DLT分析集的16例患者中，均未观察到DLT事件。纳入SS集的25例患者中有23例（92.0%）患者报告了TEAE，其中，在≥40%的患者中报告的不良事件包括白细胞计数降低、中性粒细胞计数降低、贫血、淋巴细胞计数降低、低白蛋白血症，主要为各类检查及代谢及营养类疾病。12例（48.0%）患者报告的TEAE为CTCAE≥3级，以中性粒细胞计数降低、淋巴细胞计数降低和白细胞计数降低最为常见，不同剂量组中不良事件发生率未见明显差异。2例（8.0%）患者报告了严重TEAE，为发热、血小板计数降低和感染性肺炎，均在200 mg剂量组，其中血小板计数降低和感染性肺炎经评估与研究药物相关。约1/3患者报告了导致药物暂停的TEAE，仅个例患者报告了导致药物终止或导致剂量降低的TEAE，分别在200 mg和600 mg剂量组。研究过程中，未发生实验室或临床TLS。\n该研究的安全性结果表明，在50 mg至1000 mg剂量递增过程中，FCN-338片的安全性和耐受性良好，未观察到不良事件发生率与剂量的明显相关性，不良事件可管理。”

        # 真实输入
        {safety_conclusion}

        生成的段落中，要：
        - 保留示例文案的行文结构和标点格式；语气正式
        - 仿照参考示例文案，生成临床试验中的安全性研究结论
        - 请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = safety_conclusion_template.format(safety_conclusion=safety_conclusion)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["安全性结论"] = resp

        # 13 总体结论
        all_conclusion = "临床试验名称:" + context["研究标题摘要"] + "研究药物名称:" + context["产品名称摘要"] + \
                        research_medicine+context["暴露程度大模型总结"] +"DTL总结部分："+ context["DLT大模型总结"] + context["不良事件大模型总结"] + \
                            context["TEAE大模型总结"]
        # 大模型总结部分
        safety_conclusion_template = '''你是一个资深的医学写作助手，擅长分析临床试验报告中的临床试验受试者情况。请根据以下输入，严格参照示例文案的结构和风格，生成一段专业的中文描述：

# 参考示例文案
“FCN-338是一种可口服的、高效BCL-2选择性抑制剂，临床前研究证实其有望成为新的肿瘤靶向治疗药物。本研究为一项开放标签的I/II期临床研究，旨在评价口服FCN-338片在B细胞淋巴瘤患者中的耐受性、药代动力学特征及初步抗肿瘤活性。\n该研究共入组25例患者，分别分配至7个爬坡剂量组，接受50 mg至1000 mg研究药物给药。I期剂量爬坡阶段均采用固定剂量给药。\n基于FAS集的25例患者的疗效结果表明，总体ORR为20.0%（95% CI：6.8%，40.7%），在100 mg组、200 mg组、400 mg组和600 mg组均观察到客观缓解，ORR的点估计值分别为100%（1/1），25.0%（1/4），25.0%（1/4）和28.6%（2/7）。在达到客观缓解的5例患者中，至肿瘤缓解时间为2个月左右。25例患者中，有17例（68.0%）患者发生事件肿瘤进展，估计的总体中位PFS为2.366个月（95% CI：1.840，9.429），总体中位DOR和中位OS尚无法估计。对不同肿瘤类型的最佳总体疗效进行分析结果表明，在弥漫性大B细胞淋巴瘤、滤泡性淋巴瘤和边缘区淋巴瘤患者中均观察到客观缓解，且至肿瘤缓解时间中位数在2-3个月左右。\n该研究I期部分的疗效结果表明，FCN-338片在100 mg剂量组、200 mg剂量组、400 mg剂量组和600 mg剂量组均观察到了初步的抗肿瘤活性。从不同肿瘤类型来看，在弥漫性大B细胞淋巴瘤、滤泡性淋巴瘤和边缘区淋巴瘤患者中观察到了客观缓解，为后续剂量和瘤种的进一步探索提供了依据。\n安全性方面，在纳入DLT分析集的16例患者中，均未观察到DLT事件。纳入SS集的25例患者中有23例（92.0%）患者报告了TEAE，其中，在≥40%的患者中报告的不良事件包括白细胞计数降低、中性粒细胞计数降低、贫血、淋巴细胞计数降低、低白蛋白血症，主要为各类检查及代谢及营养类疾病。12例（48.0%）患者报告的TEAE为CTCAE≥3级，以中性粒细胞计数降低、淋巴细胞计数降低和白细胞计数降低最为常见，不同剂量组中不良事件发生率未见明显差异。2例（8.0%）患者报告了严重TEAE，为发热、血小板计数降低和感染性肺炎，均在200 mg剂量组，其中血小板计数降低和感染性肺炎经评估与研究药物相关。约1/3患者报告了导致药物暂停的TEAE，仅个例患者报告了导致药物终止或导致剂量降低的TEAE，分别在200 mg和600 mg剂量组。研究过程中，未发生实验室或临床TLS。\n该研究的安全性结果表明，在50 mg至1000 mg剂量递增过程中，FCN-338片的安全性和耐受性良好，未观察到不良事件发生率与剂量的明显相关性，不良事件可管理。\nNHL患者空腹或餐后单次口服50~1000 mg FCN-338片后，均约在给药后6.00-11.0 h达峰，空腹消除半衰期约为28.5~47.9 h，消除较慢；FCN-338暴露水平（Cmax及AUCs）均随剂量增加呈增加趋势，餐后相较空腹暴露增加约1~2倍，暴露的个体间变异均较大。为使患者能尽早达到有效的药物暴露浓度，同时可减少口服药物剂量，后续FCN-338的给药方式设置为餐后给药是合理的。以上研究结果可为后续研究提供初步依据。”

# 真实输入
{all_conclusion}

生成的段落中，要：
- 保留示例文案的行文结构和标点格式；语气正式
- 仿照参考示例文案，生成临床试验中的安全性研究结论
- 请输出最终的完整段落，不要包含任何多余说明文字。/no_think'''
        # 使用 format 方法填充 all_result 到模板中
        final_prompt = safety_conclusion_template.format(all_conclusion=all_conclusion)
        resp = self.client.chat.completions.create(
            model=self.model_name,
            messages=[{"role": "user", "content": final_prompt}],
            temperature=0.2,
            max_tokens=1024
        )
        resp = resp.choices[0].message.content.strip()
        context["讨论和总体结论"] = resp

        # 生成目录
        subdoc_toc = template_file.new_subdoc()
        # 在子文档中添加一个段落
        # 在子文档中添加一个段落
        paragraph = subdoc_toc.add_paragraph()

        # 创建 TOC 字段的开始标记
        fldChar_begin = OxmlElement('w:fldChar')
        fldChar_begin.set(qn('w:fldCharType'), 'begin')

        # 创建 TOC 字段的指令文本
        instrText = OxmlElement('w:instrText')
        instrText.set(qn('xml:space'), 'preserve')
        instrText.text = 'TOC \\o "1-3" \\h \\z \\u'

        # 创建 TOC 字段的分隔标记
        fldChar_separate = OxmlElement('w:fldChar')
        fldChar_separate.set(qn('w:fldCharType'), 'separate')

        # 创建 TOC 字段的结束标记
        fldChar_end = OxmlElement('w:fldChar')
        fldChar_end.set(qn('w:fldCharType'), 'end')

        # 将上述元素添加到段落中
        r = paragraph.add_run()
        r._r.append(fldChar_begin)
        r._r.append(instrText)
        r._r.append(fldChar_separate)
        r._r.append(fldChar_end)
        # 构建返回结构
        full_context = {
            **context_home_page,  # 首页
            "目录": subdoc_toc,
            # 缩略语
            "缩略语表": abbr_subdoc,
            # 研究者姓名
            "主要研究者姓名": researcher_name,
            **context,  # 第一部分所有字段
            "研究目的摘要": research_purpose,

            # 研究背景
            "疾病背景": research_background,
            "研究药物": research_medicine,
        }

        try:
            template_file.render(full_context)
            gc.collect()
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"渲染模板失败: {e}")
        from pathlib import Path
        # 3. 构建输出路径，使用 workflow_run_id 命名
        output_dir = Path("../data/temp")
        output_dir.mkdir(parents=True, exist_ok=True)
        # 如果你想保持原始名字，也可以使用 f"{workflow_run_id}.docx"
        out_path = output_dir / f"{workflow_run_id}.docx"

        template_file.save(str(out_path))
        gc.collect()

        try:
            # 记录用时
            start_time = time.time()  # 记录开始时间
            # 重新编号表格和图片
            renumber_processed_path = output_dir / f"{workflow_run_id}_renumbered.docx"
            table_count, figure_count = renumber_tables_and_figures(str(out_path), str(renumber_processed_path))

            # 确保文件操作完成
            time.sleep(0.1)

            # 删除原始文件，将处理后的文件重命名为原始文件名
            if out_path.exists():
                out_path.unlink()
            if renumber_processed_path.exists():
                renumber_processed_path.rename(out_path)

            # 再次确保文件系统操作完成
            time.sleep(0.1)
            gc.collect()

            logger.info(f"✔️ 表格和图片重新编号完成 - 处理了 {table_count} 个表格，{figure_count} 个图片")
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(f"重新编号执行时间: {execution_time:.4f}秒")

            # 处理软回车，全部转为硬回车
            processed_path = output_dir / f"{workflow_run_id}_processed.docx"
            convert_breaks_xml_level_v2(str(out_path), str(processed_path))

            # 确保文件操作完成
            time.sleep(0.1)  # 短暂等待

            # 删除原始文件，将处理后的文件重命名为原始文件名
            if out_path.exists():
                out_path.unlink()
            if processed_path.exists():
                processed_path.rename(out_path)

            # 再次确保文件系统操作完成
            time.sleep(0.1)
            gc.collect()

            logger.info(f"✔️ 软回车转硬回车处理完成")
            end_time = time.time()  # 记录结束时间
            execution_time = end_time - start_time  # 计算总耗时
            logger.info(f"总执行时间: {execution_time:.4f}秒")
        except Exception as e:
            logger.warning(f"⚠️ 软回车转硬回车处理失败: {e}")

        try:
            # 确保文件存在且可访问
            if not out_path.exists():
                raise FileNotFoundError(f"文件不存在: {out_path}")

            # 去除多余的硬回车换行符（两个以下）
            line_breaks_processed_path = output_dir / f"{workflow_run_id}_line_breaks_processed.docx"

            remove_excessive_line_breaks_preserve_format(str(out_path), str(line_breaks_processed_path),
                                                         max_consecutive_breaks=2)

            # 确保处理完成
            time.sleep(0.1)

            # 文件替换操作
            if out_path.exists():
                out_path.unlink()
            if line_breaks_processed_path.exists():
                line_breaks_processed_path.rename(out_path)

            logger.info(f"✔️ 多余换行符去除处理完成")
            end_time = time.time()  # 记录结束时间
            execution_time = end_time - start_time  # 计算总耗时
            logger.info(f"硬回车多余去除总执行时间: {execution_time:.4f}秒")
        except Exception as e:
            logger.warning(f"⚠️ 多余换行符去除处理失败: {e}")
            logger.warning(f"详细错误信息: {str(e)}")

        # 最终确保文件存在
        if not out_path.exists():
            logger.error(f"❌ 最终文件不存在: {out_path}")
            raise FileNotFoundError(f"处理后的文件丢失: {out_path}")

        # 返回 oss 下载链接
        oss_response_data = main_upload_file(workflow_run_id, str(out_path))
        logger.info(f"✔️ 文件保存成功，正在上传到 OSS，请稍等...")

        # 删除本地临时文件（新增的删除操作）
        try:
            out_path.unlink()  # 使用 pathlib 的 unlink 方法删除文件
            logger.info(f"🗑️ 已删除临时文件: '{out_path}'")

        except Exception as e:
            logger.info(f"⚠️ 删除临时文件失败: '{out_path}'")
        gc.collect()
        return oss_response_data
