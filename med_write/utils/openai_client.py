from openai import OpenAI
from configurer.yy_nacos import Nacos
from configurer.config_reader import get_med_write_config
def get_openai_client() -> OpenAI:
    """
    初始化 OpenAI 客户端。优先使用环境变量中的配置。

    要求环境变量中必须包含：
    - OPENAI_API_KEY
    - MODEL_BASE_URL

    :return: 初始化后的 OpenAI 客户端实例
    :raises ValueError: 如果关键环境变量缺失
    """
    mse = Nacos()
    config = get_med_write_config()
    OPENAI_API_KEY = config.get("OPENAI_API_KEY")
    MODEL_BASE_URL = config.get("MODEL_BASE_URL")
    if not OPENAI_API_KEY:
        raise ValueError("❌ 缺少 OPENAI_API_KEY 环境变量")

    if not MODEL_BASE_URL:
        raise ValueError("❌ 缺少 MODEL_BASE_URL 环境变量")
    client = OpenAI(
        api_key=OPENAI_API_KEY,
        base_url=MODEL_BASE_URL
    )
    return client

if __name__ == '__main__':
    client = get_openai_client()
    print(client)