import logging
from utils.oss_utils import get_file_url, write_file
from datetime import datetime

logger = logging.getLogger(name=__name__)

def main_upload_file(workflow_id: str, file_path: str):
    try:
        # 生成文件
        now = datetime.now()
        filename = f"{workflow_id}_{now.strftime('%Y%m%d%H%M%S')}.docx"
        output_file = file_path
        logger.info("变更记录表格生成成功,文件路径：{}".format(output_file))

        # 上传并返回 URL
        write_file(output_file, filename)
        file_url = get_file_url(filename)
        logger.info(f"✅ 上传成功，{file_url}")
        # 你的字典
        response_data = {
            "success": True,
            "code": 200,
            "message": "文件上传成功",
            "result": {
                "file_url": file_url
            }
        }

        # 返回包含文件URL的JSON响应
        return response_data
    except Exception as e:
        logger.info(f"🗑️ ❌ 上传失败：: {e}")


if __name__ == "__main__":
    local_file = "./template.docx"

    print(main_upload_file("test", local_file))
