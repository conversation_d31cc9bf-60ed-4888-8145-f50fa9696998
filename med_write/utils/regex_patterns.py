import re
# 用于剥离行首数字编号，比如 “12.1.1 ”、“1）”、“（1）” 等
LEADING_NUM_PATTERN = re.compile(r'^\s*[\d一二三四五六七八九十]+(?:[.\d、\)]*\s*)+')

# ———— 编号模式：中文或阿拉伯数字 ———— #
CH_NUM = r'[一二三四五六七八九十零百千]+'
AR_NUM = r'\d+(?:\.\d+)*'
NUM_PATTERN = rf'(?:[（(]?{CH_NUM}|{AR_NUM}[)）]?)[\.、]?\s*'
# NEXT_SECTION_PATTERN = re.compile(r'^\s*(?:\d+(?:\.\d+)*[.\s、)]|[\(（][^）)]+[）\)])')
NEXT_SECTION_PATTERN = re.compile(r'^\s*\d+(?:\.\d+)*[.\s、)]')

NEXT_SECTION_PATTERN3 = re.compile(
    r'^\s*(?:表)?\s*(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①②③④⑤⑥⑦⑧⑨⑩])|(?:\([一二三四五六七八九十]+\)))'
)
# tfl 的下个章节的 pattern
NEXT_SECTION_PATTERN2 = re.compile(r'^\s*(?:表)?\s*(?:\d+(?:\.\d+)*\.?)')

HEADING_PATTERN = re.compile(
    r'^\s*(?:表)?\s*'
    r'(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①②③④⑤⑥⑦⑧⑨⑩])|(?:\([一二三四五六七八九十]+\)))'
    r'\s*(?:人口统计学资料|受试者分布情况|分析数据集|重要方案偏离情况)\s*(?:（[^）]+）)?\s*$'
)
# 2临床试验的背景
BACKGROUND_HEADING_PATTERN = re.compile(
    r'^\s*3\.1\s*(?:研发背景|研究背景|背景(?:资料)?)',
    re.IGNORECASE
)

# 3 试验目的
OBJECTIVE_HEADING_PATTERN = re.compile(
    r'^\s*(?:\d+(?:\.\d+){0,2}\.)\s*试验目的\s*$',
    re.IGNORECASE
)
PLAIN_OBJECTIVE_HEADING = re.compile(r'^\s*试验目的\s*$', re.IGNORECASE)

# 4试验流程图
FLOW_HEADING_PATTERN = re.compile(
    r'^\s*(?:\d+(?:\.\d+)*|\([^)]+\)|（[^）]+）)\s*试验流程图\s*$',
    re.IGNORECASE
)
PLAIN_FLOW_HEADING = re.compile(r'^\s*试验流程图\s*$', re.IGNORECASE)

NOTE_PATTERN = re.compile(r'^\s*注[:：]\s*.+')


# 匹配任意“表x.x.x.x”标题，用于判断何时结束当前节
NEXT_HEADING_PATTERN  = re.compile(r'^\s*表\d+\.\d+\.\d+\.\d+.*$')
# 匹配注释行
# 如果遇到纯中文标题（例如 “方案摘要”），或数字编号开头（例如 “1.”、“3.2.1、”），就认为是新的章节
NEXT_SECTION_PATTERN_abbr = re.compile(
    r'^\s*'                                # 行首可有空白
    r'(?:'                                 # 二选一：
      r'\d+(?:\.\d+)*[\.、]\s*.+?'     #   - 数字编号（1.、1.1.、3.2.1.）后跟任意文字
    r'|'                                   # 或
      r'[\u4e00-\u9fa5 ]+'               #   - 纯中文（不含中文句号/问号等标点）
    r')\s*$'                              # 行尾可有空白
)
HEADING_PATTERN_pop = re.compile(
    r'^\s*(?:表)?\s*(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①②③④⑤⑥⑦⑧⑨⑩])|(?:\([一二三四五六七八九十]+\)))'
    r'\s*(?:人口统计学资料)\s*(?:（[^）]+）)?\s*$'
)
NEXT_SECTION_PATTERN_pop = re.compile(r'^\s*(?:表)?\s*(?:\d+(?:\.\d+)*\.?)')
HEADING_PATTERN_372 = re.compile(
    r'^\s*(?:表)?\s*(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①②③④⑤⑥⑦⑧⑨⑩])|(?:\([一二三四五六七八九十]+\)))'
    r'\s*(?:受试者分布情况|分析数据集|重要方案偏离情况)\s*(?:（[^）]+）)?\s*$'
)
NEXT_SECTION_PATTERN376 = re.compile(
    r'^\s*(?:表)?\s*(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①②③④⑤⑥⑦⑧⑨⑩])|(?:\([一二三四五六七八九十]+\)))'
)
HEADING_PATTERN677 = re.compile(r'^\s*表9\.1\.\d+\.\d+.*$')


def make_heading_pattern(section_name: str) -> re.Pattern:
    # 支持 (Continued) 标题
    esc = re.escape(section_name)

    return re.compile(
        rf'^\s*(?:表)?\s*'
        rf'(?:(?:\d+(?:\.\d+)*\.?)|(?:[一二三四五六七八九十]+[、. ]?)|(?:[①-⑩])|(?:\([一二三四五六七八九十]+\)))'
        rf'\s*{esc}\s*(?:（[^）]+）)?(?:\s*\(Continued\))?\s*$'
    )
def make_heading_pattern_abbr(section_name: str) -> re.Pattern:
    """
    生成匹配目标章节名的正则：
      - 可选阿拉伯数字编号前缀（如“1.”、“3.2.1、”）
      - 紧接精确的章节名称（如“缩略词列表”）
      - 可选小括号注释（如“(Continued)”或“(附录)”）
    """
    esc = re.escape(section_name)
    return re.compile(
        rf'^\s*'                             # 行首可有空白
        rf'(?:\d+(?:\.\d+)*[\.、]\s*)?'  # 可选数字多级编号 + “.”或“、” + 空白
        rf'{esc}'                            # 精确匹配章节名
        rf'\s*(?:\([^)]+\))?\s*$'        # 可选括号内注释 + 行尾空白
    )

def make_heading_pattern1(section_name: str) -> re.Pattern:
    esc = re.escape(section_name)
    # 如果用户传入的是“表9.2.1”或“9.2.1”这种纯数字+点的前缀
    if re.fullmatch(r'(?:表)?\d+(?:\.\d+)*', section_name):
        # ^\s*表9\.2\.1(?:\.\d+)*\.?\s+.*$
        #   → 匹配以“表9.2.1”起头，后面可以跟“.x”，然后至少有一个空格再跟正文
        return re.compile(rf'^\s*{esc}(?:\.\d+)*\.?\s+.*$')
    return re.compile(
        rf'^\s*(?:表)?\s*'
        rf'(?:(?:\d+(?:\.\d+)*\.?)|'           # 阿拉伯数字编号
        rf'(?:[一二三四五六七八九十]+[、. ]?)|'   # 中文数字编号
        rf'(?:[①-⑩])|'                          # 圆括号编号
        rf'(?:\([一二三四五六七八九十]+\))'     # （中文数字）
        rf')\s*.*?{esc}.*?(?:（[^）]*）)?(?:\s*\(Continued\))?\s*$'
    )