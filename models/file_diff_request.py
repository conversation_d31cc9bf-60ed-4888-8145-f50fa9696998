from pydantic import BaseModel, Field

from models.file_info import FileInfo


class FileDiffRequest(BaseModel):
    original_file: FileInfo = Field(None, title="original file")
    revised_file: FileInfo = Field(None, title="revised file")


class SingleFileDiffRequest(BaseModel):
    file: FileInfo = Field(None, title="file")

class DocDiffConvertRequest(BaseModel):
    revised_json : list[dict] = Field(None, title="revised json")
    summary_result : list[dict] = Field(None, title="summary json")
    icf_first_table_info : list[dict] = Field(None, title="知情同意书提取json格式的试验方案编号和变更前后的版本号信息")
