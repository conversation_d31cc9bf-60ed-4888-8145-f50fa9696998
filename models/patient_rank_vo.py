# models/patient_rank_vo.py

from typing import List
from pydantic import BaseModel, Field

class PatientRankingRequest(BaseModel):
    """
    患者排名与报告导出API的请求体模型
    """
    patient_json_strings: List[str] = Field(
        ...,  # '...' 表示该字段为必需字段
        title="患者计分结果JSON字符串列表",
        description="由核心计分逻辑(main函数)为每位患者生成的评估结果JSON字符串所组成的列表。"
    )
    business_id: str = Field(
        "patient-rank",
        title="业务标识符",
        description="用于生成报告文件名的业务前缀，例如 '血管癌'。"
    )