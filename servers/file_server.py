# -*- coding: utf-8 -*-
from datetime import datetime
from pathlib import Path
import os

import cv2
import oss2
from oss2.credentials import EnvironmentVariableCredentialsProvider
from urllib.parse import unquote

from models.file_info import FileInfo

os.environ["OSS_ACCESS_KEY_ID"] = 'LTAI5tSMtzYQ5GVPCm8njDYp'
os.environ["OSS_ACCESS_KEY_SECRET"] = '******************************'

ossEndpoint = 'oss-cn-hangzhou.aliyuncs.com'
bucketName = 'yiya-dev'

# 填写Endpoint对应的Region信息，例如cn-hangzhou。注意，v4签名下，必须填写该参数
region = "cn-hangzhou"

# 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
auth = oss2.ProviderAuth(EnvironmentVariableCredentialsProvider())

# yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
# 填写Bucket名称，例如examplebucket。
bucket = oss2.Bucket(auth, "https://" + ossEndpoint, bucketName, region=region)


def get_file_url(file_key):
    # 生成下载文件的签名URL，有效时间为一年
    # 设置slash_safe为True，OSS不会对Object完整路径中的正斜线（/）进行转义，此时生成的签名URL可以直接使用。
    url = bucket.sign_url('GET', file_key, 604800, slash_safe=True)

    # return unquote(url)
    return url


def upload_local_file(local_path, file_name):
    file_key = build_oss_key(file_name)
    bucket.put_object_from_file(file_key, local_path)
    return file_key


def put_object(np_image, file_name):
    # 将 np_image 转换为 JPEG 字节流
    _, img_encoded = cv2.imencode('.jpg', np_image)
    bucket.put_object(file_name, img_encoded.tostring())


def put_pic(np_image, file_name):
    bucket.put_object(file_name, np_image)


def buildOssKey(fileName, uid='tmp'):
    return 'oss/' + uid + "/" + fileName;


def upload_zip_local_file(local_path, file_name):
    file_key = zip_build_oss_key(file_name)
    bucket.put_object_from_file(file_key, local_path)
    return file_key


def zip_build_oss_key(fileName, uid='tmp'):
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    uid = formatted_time if uid == 'tmp' else uid
    return f"rename_unzip/oss-{uid}-{fileName}"


def build_oss_key(fileName, uid='tmp'):
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    uid = formatted_time if uid == 'tmp' else uid
    return f"oss-{uid}-{fileName}"


def upload_and_return_file_info(file_path, file_name) -> FileInfo:
    file_key = upload_local_file(file_path, file_name)
    file_url = get_file_url(file_key)
    return FileInfo(file_key=file_key, file_name=file_name, file_path=file_path, file_url=file_url)



if __name__ == "__main__":
    local_path = '/Users/<USER>/IdeaProjects/mypython/work/my_home.png'
    # 获取文件名（包括扩展名）
    file_path = Path(local_path)
    file_name_with_extension = file_path.name
    file_key = upload_local_file(local_path, file_name_with_extension)
    print("file_key:", file_key)

    # 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
    url = get_file_url(file_key)
    print('签名URL的地址为：', url)
