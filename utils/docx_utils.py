import copy
import os
import re
import json
from datetime import datetime
from zipfile import ZipFile
import bs4

from bs4 import BeautifulSoup
from docx import Document
from docx.shared import RGBColor

from config.env_config import UPLOAD_FOLDER
from models.file_info import FileInfo
from utils.oss_utils import write_file, get_file_url
from logger.logger import app_logger

import difflib


def read_docx_paragraphs(file_path):
    """Read a .docx file and return the text of each paragraph along with its index."""
    doc = Document(file_path)
    paragraphs = []
    for idx, para in enumerate(doc.paragraphs, start=1):
        if para.text.strip():  # Ignore empty paragraphs
            paragraphs.append({
                'text': para.text,
                'paragraph_number': idx
            })
    return paragraphs


def compare_paragraphs(original_paragraphs, revised_paragraphs):
    """Compare two lists of paragraphs and return only those that have changed."""
    differ = difflib.Differ()
    changes = []

    max_length = max(len(original_paragraphs), len(revised_paragraphs))

    # Pad shorter list with empty dictionaries to match lengths
    original_paragraphs += [{'text': '', 'paragraph_number': i + len(original_paragraphs)}
                            for i in range(max_length - len(original_paragraphs))]
    revised_paragraphs += [{'text': '', 'paragraph_number': i + len(revised_paragraphs)}
                           for i in range(max_length - len(revised_paragraphs))]

    for i in range(max_length):
        orig_para = original_paragraphs[i]['text']
        rev_para = revised_paragraphs[i]['text']

        # Only consider non-empty paragraphs for comparison
        if orig_para or rev_para:
            diff = list(differ.compare([orig_para], [rev_para]))
            if any(line[0] != ' ' for line in diff):  # Check if there are any differences
                changes.append({
                    'original': orig_para,
                    'revised': rev_para,
                    'paragraph_number': i + 1  # Paragraph numbers start from 1
                })

    return changes


def get_doc_diff_data(docx_path):
    """
    从Word文档中提取修订数据并返回JSON格式结果
    """
    # 读取文档内容
    with ZipFile(docx_path) as doc:
        xml_content = doc.read("word/document.xml").decode("utf-8")

    soup = BeautifulSoup(xml_content, "xml")
    paragraphs = soup.find_all("w:p")

    change_list = []

    for para_num, para in enumerate(paragraphs, 1):
        # 检测是否存在修订
        if not para.find(["w:ins", "w:del"]):
            continue

        # 准备原始和修订后的段落副本
        original_para = copy.deepcopy(para)
        revised_para = copy.deepcopy(para)

        # 处理原始内容（移除新增内容）
        for ins in original_para.find_all("w:ins"):
            ins.decompose()

        # 处理修订内容（移除删除内容）
        for deletion in revised_para.find_all("w:del"):
            deletion.decompose()

        # 提取文本内容
        original_text = "".join(
            t.text for t in original_para.find_all("w:t")
        ).strip()

        revised_text = "".join(
            t.text for t in revised_para.find_all("w:t")
        ).strip()

        # 跳过没有实际变化的修订
        if original_text == revised_text:
            continue

        # 构建变更记录
        change_item = {
            "paragraph_number": para_num,
            "original": [{
                "text": original_text,
                "paragraph_number": para_num
            }],
            "revised": [{
                "text": revised_text,
                "paragraph_number": para_num
            }]
        }

        change_list.append(change_item)

    return change_list


# soc v2

def get_doc_diff_data_v2(docx_path):
    """
    从Word文档中提取正文、页眉、页脚的修订数据，返回结构化结果
    """
    change_list = []

    with ZipFile(docx_path) as doc:
        # 处理正文
        if "word/document.xml" in doc.namelist():
            xml_content = doc.read("word/document.xml").decode("utf-8")
            change_list.extend(process_part(xml_content, "body", "document.xml"))

        # 处理页眉（支持多个页眉）
        for header_file in [f for f in doc.namelist() if f.startswith("word/header")]:
            xml_content = doc.read(header_file).decode("utf-8")
            change_list.extend(process_part(xml_content, "header", header_file))

        # 处理页脚（支持多个页脚）
        for footer_file in [f for f in doc.namelist() if f.startswith("word/footer")]:
            xml_content = doc.read(footer_file).decode("utf-8")
            change_list.extend(process_part(xml_content, "footer", footer_file))

    return change_list


def process_part(xml_content, part_type, source_file):
    """
    处理单个XML部分（正文/页眉/页脚）
    返回该部分的修订记录
    """
    soup = BeautifulSoup(xml_content, "xml")
    paragraphs = soup.find_all("w:p")
    changes = []

    for para_num, para in enumerate(paragraphs, 1):
        # 跳过没有修订的段落
        if not para.find(["w:ins", "w:del"]):
            continue

        # 生成原始和修订文本
        original_text, revised_text = process_paragraph(para)

        # 过滤无实质变化的修订
        if original_text == revised_text:
            continue

        changes.append({
            "type": part_type,
            "source_file": source_file.split("/")[-1],  # 保留文件名即可
            "part_num": para_num,
            "original": original_text,
            "revised": revised_text
        })

    return changes


def process_paragraph(para):
    """
    处理单个段落，返回原始文本和修订后文本的元组
    """
    # 创建副本以避免修改原始数据
    original = copy.deepcopy(para)
    revised = copy.deepcopy(para)

    # 清理不需要的元素
    for tag in original.find_all("w:ins"):
        tag.decompose()
    for tag in revised.find_all("w:del"):
        tag.decompose()

    # 提取纯文本（考虑复杂文档结构）
    def extract_text(element):
        return "".join(t.text for t in element.find_all(["w:t", "w:delText"]))

    return (
        extract_text(original).strip(),
        extract_text(revised).strip()
    )


def compare_docx_files(original_file, revised_file):
    original_paragraphs = read_docx_paragraphs(original_file)
    revised_paragraphs = read_docx_paragraphs(revised_file)

    # Compare the paragraphs and get changes
    changes = compare_paragraphs(original_paragraphs, revised_paragraphs)
    return changes


global_index = 1


def get_doc_diff_data_v3(docx_path):
    """
    从Word文档中提取正文、页眉、页脚以及表格中的修订数据，返回结构化结果
    """
    change_list = []
    global global_index  # 确保我们在修改全局变量
    global_index = 1  # 之前的代码有问题，global_index 的值不会重置一直累加
    with ZipFile(docx_path) as doc:
        # 处理正文
        if "word/document.xml" in doc.namelist():
            xml_content = doc.read("word/document.xml").decode("utf-8")
            # 从 .docx 文件中的 styles.xml 提取段落样式映射表（styleId -> styleName）
            style_map = parse_style_map_from_docx(docx_path)
            change_list.extend(process_part_v1(xml_content, "document", "document.xml", style_map))

        # 处理页眉
        for header_file in [f for f in doc.namelist() if f.startswith("word/header")]:
            xml_content = doc.read(header_file).decode("utf-8")
            change_list.extend(process_part_v1(xml_content, "header", header_file))

        # 处理页脚
        for footer_file in [f for f in doc.namelist() if f.startswith("word/footer")]:
            xml_content = doc.read(footer_file).decode("utf-8")
            change_list.extend(process_part_v1(xml_content, "footer", footer_file))

    return change_list


def process_part_v1(xml_content, part_type, source_file, style_map=None):
    """
    处理XML部分（正文/页眉/页脚/表格），提取修订内容
    """
    global global_index
    soup = BeautifulSoup(xml_content, "xml")
    changes = []
    paragraphs = soup.find_all("w:p")  # 段落列表
    tables = soup.find_all("w:tbl")  # 表格列表

    # 处理正文的段落
    if part_type == "document":
        for para_num, para in enumerate(paragraphs, 1):
            if para.find_parents("w:tc"):  # 如果此段落属于表格（tc:table cell），跳过，这块表格处理部分单独处理
                continue
            if para.find(["w:ins", "w:del"]):  # 查找 para 这个段落元素下，是否包含任意一个 <w:ins> 或 <w:del> 标签
                original_text, revised_text = process_paragraph_v1(para)
                if original_text != revised_text:
                    heading = find_nearest_heading(paragraphs, para_num, part_type, style_map)

                    # 直接添加到changes，每个段落独立处理
                    changes.append({
                        "type": part_type,
                        "original": original_text,
                        "revised": revised_text,
                        "index": global_index,  # 全局唯一索引
                        "topic": heading,
                        "source": source_file
                    })
                    global_index += 1  # 递增全局索引
    # 处理页眉页脚：header或者 footer
    merged_changes = {}  # 用于合并同一段落的修订内容缓存
    if part_type == "header" or part_type == "footer":
        for para_num, para in enumerate(paragraphs, 1):
            if para.find_parents("w:tc"):  # 如果此段落属于表格（tc:table cell），跳过，这块表格处理部分单独处理
                continue
            if para.find(["w:ins", "w:del"]):  # 查找 para 这个段落元素下，是否包含任意一个 <w:ins> 或 <w:del> 标签
                original_text, revised_text = process_paragraph_v1(para)
                if original_text != revised_text:
                    heading = find_nearest_heading(paragraphs, para_num, part_type)

                    unique_key = (part_type, source_file, heading)  # 例如 ('document', 'document.xml', '十分感谢您考虑参加这项研究。')

                    if unique_key in merged_changes:
                        merged_changes[unique_key]["original"] += " " + original_text
                        merged_changes[unique_key]["revised"] += " " + revised_text
                    else:
                        # {('document', 'document.xml', '十 研究'):
                        #      {'index': 1,
                        #        'original': '同时需要采集您10张（若您患的是胃癌、胃食管结合处癌或乳腺癌，则需要提供12张）的未经过染色的肿瘤组织切片转送到中心实验室，用于FGFR2异常检测。这些标本可以是已经存档的标本，也可以是新采集的肿瘤组织标本。如果您没有既往存档的6个月内的肿瘤组织样本或提供的切片数量不足以完成上述检测，或研究医生认为活检对您的疾病诊断会更准确，您将被邀请进行新鲜肿瘤组织活检（切除、空心针或细针穿刺）。',
                        #        'revised': '同时需要采集您10张（若您患的是胃癌、胃食管结合处癌或乳腺癌，则需要提供12张）的未经过染色的肿瘤组织切片转送到中心实验室，用于FGFR2异常检测。这些标本可以是已经存档的标本，也可以是新采集的肿瘤组织标本。如果您没有既往存档的5年内的肿瘤组织样本或提供的切片数量不足完成上述检测，或研究医生认为活检对您的疾病诊断会更准确，您将被邀请进行新鲜肿瘤组织活检（切除、空心针或细针穿刺）。',
                        #        'source': 'document.xml',
                        #        'topic': '十分感谢您考虑参加这项研究。',
                        #        'type': 'document'}}
                        merged_changes[unique_key] = {
                            "type": part_type,
                            "original": original_text,
                            "revised": revised_text,
                            "index": global_index,  # 全局唯一索引
                            "topic": heading,
                            "source": source_file
                        }
                        global_index += 1  # 递增全局索引

    # 添加合并后的段落修订
    changes.extend(merged_changes.values())

    # 处理表格
    def is_table_continued(table):
        prev_sibling = table.find_previous_sibling("w:tbl")
        if prev_sibling and prev_sibling.find("w:tblPr") and prev_sibling.find("w:tblPr").find("w:tblHeader"):
            return True
        return False

    current_table_index = 0

    for table in tables:
        if not is_table_continued(table):
            current_table_index += 1

        cell_changes = {}

        rows = table.find_all("w:tr")
        for row_index, row in enumerate(rows, 1):
            cells = row.find_all("w:tc")
            total_cols = len(cells)

            for col_index, cell in enumerate(cells, 1):
                if col_index > total_cols:
                    continue

                grid_span = cell.find("w:gridSpan")
                span = int(grid_span.get("w:val", "1")) if grid_span else 1

                original_texts = []
                revised_texts = []

                for para in cell.find_all("w:p"):
                    if para.find(["w:ins", "w:del"]):
                        original_text, revised_text = process_paragraph_v1(para)
                        if original_text != revised_text:
                            original_texts.append(original_text)
                            revised_texts.append(revised_text)
                # 合并同一个单元格cell 内部多个修改，构造唯一键
                if original_texts and revised_texts:
                    combined_original = " ".join(original_texts)
                    combined_revised = " ".join(revised_texts)
                    combined_key = (current_table_index, combined_original, combined_revised)

                    if combined_key not in cell_changes:
                        cell_changes[combined_key] = []

                    for span_col in range(col_index, min(col_index + span, total_cols + 1)):
                        cell_changes[combined_key].append((row_index, span_col))

        # 处理合并后的变更
        for (table_idx, original, revised), positions in cell_changes.items():
            unique_positions = list(set(positions))
            position_str = format_table_positions(unique_positions)

            changes.append({
                "type": "table",
                "original": original.strip(),
                "revised": revised.strip(),
                "index": global_index,  # 全局唯一索引
                "topic": f"表格{table_idx}，{position_str}",
                "source": source_file
            })
            global_index += 1  # 递增全局索引

    return changes


def format_table_positions(positions):
    """
    格式化表格位置信息，支持区间表示
    """
    if not positions:
        return ""

    positions = sorted(positions)
    rows = [p[0] for p in positions]
    cols = [p[1] for p in positions]

    min_row, max_row = min(rows), max(rows)
    min_col, max_col = min(cols), max(cols)

    if len(set(rows)) == 1:
        if min_col == max_col:
            return f"第{min_row}行 第{min_col}列"
        return f"第{min_row}行 第{min_col}-{max_col}列"
    elif len(set(cols)) == 1:
        if min_row == max_row:
            return f"第{min_row}行 第{min_col}列"
        return f"第{min_row}-{max_row}行 第{min_col}列"
    else:
        return f"第{min_row}-{max_row}行 第{min_col}-{max_col}列"


def process_paragraph_v1(para):
    """
    提取段落的原始文本和修订后文本：
    	•	原始版本（original）：不要新增的（删掉 <w:ins>）
	    •	修订版本（revised）：不要被删掉的（删掉 <w:del>）
    """
    original = copy.deepcopy(para)
    revised = copy.deepcopy(para)

    for tag in original.find_all("w:ins"):
        tag.decompose()
    for tag in revised.find_all("w:del"):
        tag.decompose()

    def extract_text(element):
        return "".join(t.text for t in element.find_all(["w:t", "w:delText"])).strip()

    return extract_text(original), extract_text(revised)


def find_nearest_heading(paragraphs, para_index, part_type, style_map=None):
    """
    向前查找最近的标题：
    1. 优先查找 w:pStyle 标识的标准标题（Heading1, Heading2...）
    2. 如果找不到，则使用正则匹配类似标题的文本
    3. 会跳过普通正文，继续往前找

    参数：
        paragraphs: 段落列表
        para_index: 当前段落索引
        part_type: 类型（"header", "footer" ,document.xml等）
        style_map: 样式ID到样式名的映射字典（从parse_style_map_from_docx获得）
    """
    if part_type == "header":
        return "页眉"
    elif part_type == "footer":
        return "页脚"

    for i in range(para_index - 1, -1, -1):  # 从当前段落向前回溯
        para = paragraphs[i]
        if para.find_parents("w:tc"):  # 如果此段落属于表格（tc:table cell），跳过，因为有 badcase：表格里的2025年2月20日作为标题
            continue
        text = "".join(t.text for t in para.find_all("w:t")).strip()

        # Step 1: 查找标准标题
        p_style = para.find("w:pStyle")  # 段落属性，例如<w:pPr><w:pStyle w:val="3"/>
        if p_style:
            style_val = p_style.get("w:val", "")

            # 使用样式映射表查找真实样式名
            if style_map and style_val in style_map:
                style_name = style_map[style_val].lower()
                if "heading" in style_name:  # 支持 "heading 1", "Heading1", "标题 1" 等格式
                    return text  # 找到正式标题，立即返回

            # 兼容性处理：如果没有样式映射表或查找失败，尝试直接匹配
            elif style_val.lower().startswith("heading"):
                return text

        # Step 2: 退回正则匹配标题格式（可能是自定义标题）
        title_pattern = r"^\s*(\d+(?:\.\d+)*[\s、]*|[一二三四五六七八九十]+[\s、]+)\S+.*[^。！？；,，]$"
        if re.match(title_pattern, text):
            return text  # 找到符合标题格式的文本

    return ""  # 如果找不到，返回空字符串


# 生成报告
def get_revised_data(revised_json_str, summary_result):
    # 获取修订记录、摘要结果
    revised_data = revised_json_str
    summary_data = summary_result
    # 合并修订记录和摘要结果
    joined_result = []
    for item in revised_data:
        section_type = item["type"]
        index = item["index"]
        original_text = item["original"]
        revised_text = item["revised"]
        topic = item["topic"]
        comments = ''
        for summary_item in summary_data:
            if summary_item["index"] == index:
                comments = summary_item["comments"]
        joined_result.append({
            "type": section_type,
            "index": index,
            "topic": topic,
            "original": original_text,
            "revised": revised_text,
            "comments": comments
        })

    return joined_result


def highlight_diff(original_text, revised_text):
    """
    对比原始文本和修订文本，生成带有 HTML 样式的标记：
    - 原始文本（origin）: 删除部分灰色+中划线
    - 修订文本（revised）: 新增部分绿色+加粗（删除的部分移除）
    """
    sm = difflib.SequenceMatcher(None, original_text, revised_text)
    result_origin = []
    result_revised = []

    for tag, i1, i2, j1, j2 in sm.get_opcodes():
        if tag == 'equal':  # 相同部分
            result_origin.append(original_text[i1:i2])
            result_revised.append(revised_text[j1:j2])
        elif tag == 'delete':  # 被删除的内容（仅在原始文本中保留，添加灰色+中划线）
            result_origin.append(f'<del style="color:gray;text-decoration:line-through">{original_text[i1:i2]}</del>')
        elif tag == 'insert':  # 新增的内容（仅在修订文本中保留，添加绿色+加粗）
            result_revised.append(f'<ins style="color:green;font-weight:bold">{revised_text[j1:j2]}</ins>')
        elif tag == 'replace':  # 替换的内容（删除的部分灰色+中划线，新增的部分绿色+加粗）
            result_origin.append(f'<del style="color:gray;text-decoration:line-through">{original_text[i1:i2]}</del>')
            result_revised.append(f'<ins style="color:green;font-weight:bold">{revised_text[j1:j2]}</ins>')

    return ''.join(result_origin), ''.join(result_revised)


def html_to_table_cell(html_text, cell):
    """
    将 HTML 文本转换为 Word 表格单元格内容，并应用格式：
    - `<ins>` 绿色+加粗
    - `<del>` 灰色+中划线
    """
    soup = BeautifulSoup(html_text, 'html.parser')
    paragraph = cell.add_paragraph()

    # 清除单元格中的现有内容
    if len(cell.paragraphs) > 0:
        p = cell.paragraphs[0]
        p._element.getparent().remove(p._element)
        p._p = None
        p._element = None

    # 递归处理HTML元素
    def process_element(element, parent_run=None):
        if isinstance(element, bs4.element.Tag):
            if element.name == 'ins':
                run = paragraph.add_run(element.get_text())
                run.font.color.rgb = RGBColor(0, 128, 0)  # 绿色
                run.bold = True  # 加粗
            elif element.name == 'del':
                run = paragraph.add_run(element.get_text())
                run.font.color.rgb = RGBColor(128, 128, 128)  # 灰色
                run.font.strike = True  # 中划线
            else:
                # 处理其他标签的子元素
                for child in element.contents:
                    process_element(child)
        elif isinstance(element, bs4.element.NavigableString):
            # 处理纯文本
            paragraph.add_run(str(element))

    # 处理所有顶层元素
    for element in soup.contents:
        process_element(element)


def generate_change_report(revised_json_str, summary_result, icf_first_table_info, template_path):
    """
    根据模板写入变更记录表格
    """
    doc = Document(template_path)
    if not doc.tables:
        raise ValueError("模板中未找到表格")

    # 写入模板文件的第一个表格

    table = doc.tables[0]
    # 填写模板文档的第一个表格
    if len(table.rows) >= 2:
        # 第1行第2列（A）- 试验方案编号
        table.cell(0, 1).text = icf_first_table_info[0]["protocol_number"]

        # 第2行第2列（B）- 原始版本号
        table.cell(1, 1).text = icf_first_table_info[0]["original_version"]

        # 第2行第4列（C）- 修订后版本号（注意索引是3，因为是第4列）
        if len(table.columns) > 3:
            table.cell(1, 3).text = icf_first_table_info[0]["revised_version"]

    # 写入模版文件的第二个表格
    table = doc.tables[1]
    change_list = get_revised_data(revised_json_str, summary_result)
    try:
        for change in change_list:
            row = table.add_row()
            cells = row.cells

            # 类型映射
            type_map = {"header": "页眉", "footer": "页脚", "body": "正文"}
            cells[0].text = change["topic"]

            # 对比差异
            original = change["original"]
            revised = change["revised"]
            highlight_origin, highlight_revised = highlight_diff(original, revised)

            cells[1].text = str(change["index"])

            # 原文部分（origin）: 删除的内容变灰+中划线
            html_to_table_cell(highlight_origin, cells[2])

            # 修订部分（revised）: 仅保留新增内容，绿色+加粗
            html_to_table_cell(highlight_revised, cells[3])

            # 备注信息
            cells[4].text = change["comments"]

            # 自动调整列格式
            for column in table.columns:
                for cell in column.cells:
                    for paragraph in cell.paragraphs:
                        paragraph.paragraph_format.space_after = 0
    except Exception as e:
        app_logger.error("MarkItDown Failed: ", e)

    # 生成文件
    now = datetime.now()
    filename = f"change_report_{now.strftime('%Y%m%d%H%M%S')}.docx"
    output_file = os.path.join(os.path.dirname(__file__), '..', UPLOAD_FOLDER, filename)
    doc.save(output_file)
    app_logger.info("变更记录表格生成成功,文件路径：{}".format(output_file))

    # 上传并返回 URL
    write_file(output_file, filename)
    file_url = get_file_url(filename)
    os.remove(output_file)

    return FileInfo(file_key=filename, file_name=filename, file_url=file_url)


def parse_icf_first_table(docx_path):
    """
    从informed consent form知情同意书的docx文件的第一个表格中提取试验方案编号和变更前后的版本号信息
    """
    with ZipFile(docx_path) as doc:
        if "word/document.xml" not in doc.namelist():
            return

        xml_content = doc.read("word/document.xml").decode("utf-8")
        soup = BeautifulSoup(xml_content, "xml")

        # 找到第一个表格
        first_table = soup.find("w:tbl")
        if not first_table:
            return

        rows = first_table.find_all("w:tr")

        protocol_number = ""
        original_version = ""
        revised_version = ""

        for row in rows:
            cells = row.find_all("w:tc")
            if len(cells) < 2:
                continue

            # 提取第一个cell的文本
            first_cell_text = "".join(t.text for t in cells[0].find_all("w:t")).strip()

            # 检查是否是试验方案编号相关的行
            protocol_keywords = ["试验方案编号", "方案编号", "方案号"]
            if any(keyword in first_cell_text for keyword in protocol_keywords):
                protocol_number = "".join(t.text for t in cells[1].find_all("w:t")).strip()

            # 检查是否是版本号相关的行
            version_keywords = ["版本号", "版本号码", "版本编号", "方案版本"]
            if any(keyword in first_cell_text for keyword in version_keywords):
                # 使用修订检测逻辑提取原始和修订后的版本
                second_cell = cells[1]
                if second_cell.find(["w:ins", "w:del"]):
                    original_text, revised_text = process_paragraph_v1(second_cell)
                    original_version = original_text
                    revised_version = revised_text
                else:
                    # 如果没有修订，原始和修订版本相同
                    version_text = "".join(t.text for t in cells[1].find_all("w:t")).strip()
                    original_version = version_text
                    revised_version = version_text
    return [{
        "protocol_number": protocol_number,
        "original_version": original_version,
        "revised_version": revised_version
    }]


def parse_style_map_from_docx(docx_path):
    """
    从 .docx 文件中的 styles.xml 提取段落样式映射表（styleId -> styleName）。

    在 Word 文档的结构中，段落样式通常以 <w:pStyle w:val="X"/> 的形式
    出现在 document.xml 中（正文），其中 "X" 是样式 ID（如 "1"、"3"、"79"），
    并不直接对应样式的名称（如 "Heading 1"、"Normal"）。

    样式的真实名称需通过解析 styles.xml 中的 <w:style> 元素获得，其结构形如：
        <w:style w:type="paragraph" w:styleId="3">
            <w:name w:val="heading 2"/>
        </w:style>

    本函数会打开 docx 文件（zip 格式），提取 word/styles.xml 文件，
    并返回一个字典形式的映射表，用于在解析 document.xml 时根据 w:val 找到实际样式名。

    参数：
        docx_path (str): .docx 文件路径

    返回：
        dict[str, str]: 样式ID（styleId）到样式名（w:name w:val）的映射字典，例如：
            {
                "1": "Normal",
                "2": "heading 1",
                "3": "heading 2",
                ...
            }
    """
    style_map = {}
    with ZipFile(docx_path) as docx_zip:
        with docx_zip.open("word/styles.xml") as styles_file:
            soup = BeautifulSoup(styles_file.read(), "xml")
            for style in soup.find_all("w:style", {"w:type": "paragraph"}):
                style_id = style.get("w:styleId")
                name_tag = style.find("w:name")
                if style_id and name_tag:
                    style_name = name_tag.get("w:val", "")
                    style_map[style_id] = style_name
    return style_map


if __name__ == '__main__':
    revised_file_path = "./临床研究方案_副本.docx"
    aa = parse_style_map_from_docx(revised_file_path)
    print(aa)
    # changes = get_doc_diff_data_v3(revised_file_path)
    # print('changes', changes)
    # print(parse_icf_first_table("./KNT-0916 EO002-CS002 生物学检测知情同意书_复旦肿瘤专用版0224痕迹版.docx"))
#     # Define paths for your documents
#     # original_file = '../test/diff_1.docx'
#     # revised_file = '../test/diff_2.docx'
#     #
#     # # Read paragraphs from both documents
#     # original_paragraphs = read_docx_paragraphs(original_file)
#     # revised_paragraphs = read_docx_paragraphs(revised_file)
#     #
#     # # Compare the paragraphs and get changes
#     # changes = compare_paragraphs(original_paragraphs, revised_paragraphs)
#     file = "../test/测试批注翻译.docx"
#     changes = get_doc_diff_data_v3(file);
#
#     # Output the changes
#     for change in changes:
#         print(change)
