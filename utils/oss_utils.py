# -*- coding: utf-8 -*-

import oss2
import os

LASER = "/data/LASER_yiya"

# 从配置文件中获取Access Key ID和Access Key Secret
access_key_id = "LTAI5tSMtzYQ5GVPCm8njDYp"
access_key_secret = "******************************"

# 使用获取的RAM用户的访问密钥配置访问凭证
auth = oss2.AuthV4(access_key_id, access_key_secret)

# 使用环境变量中获取的RAM用户访问密钥配置访问凭证
bucket = 'yiya-dev'
bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', bucket, region="cn-hangzhou")


def download_file(key, save_path):
    folder = os.path.dirname(save_path)
    if not os.path.exists(folder):
        os.makedirs(folder)
    bucket.get_object_to_file(key, save_path)


def write_file(local_path, key):
    bucket.put_object_from_file(key, local_path)


def get_file_url(key: str):
    expiration = 24 * 60 * 60
    return bucket.sign_url('GET', key, expiration)


if __name__ == '__main__':
    download_file(
        "2124_3161_20241020215131.json",
        "./2124_3161_20241020215131.json")

    # download_file(
    #     "cos/20/Niraparib PPK Modeling Report_Zai_Draft1_UPDATED_20190110_unQCd (1)_parse3524935558657725957.json",
    #     "./1_tgt.json")
    # src_data_2 = json.load(open("2_src.json", "r"))
    # segmentList = src_data_2['segmentList']
    # tgt_data = {"charCount": 11058, "language": "en-US", "rowCount": 1408 }
    # tgt_data_segmentList = []
    # for d in segmentList:
    #     d_tmp = copy.deepcopy(d)
    #     d_tmp["srcText"], d_tmp[]
